<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .config-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🤖 Gemini 2.0 Flash 配置测试</h1>
    
    <div class="test-container">
        <h2>配置信息</h2>
        <div class="config-info">
            <p><strong>模型版本:</strong> gemini-2.5-flash-lite-preview-06-17</p>
            <p><strong>API密钥:</strong> AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s (已内嵌)</p>
            <p><strong>智能ID填充:</strong> 基于api return id list.md数据映射</p>
            <p><strong>子分类限制:</strong> 仅支持接机(2)、送机(3)、包车(4)</p>
        </div>
    </div>

    <div class="test-container">
        <h2>API连接测试</h2>
        <button onclick="testApiConnection()">测试API连接</button>
        <div id="connectionResult"></div>
    </div>

    <div class="test-container">
        <h2>智能解析测试</h2>
        <textarea id="testInput" placeholder="输入订单描述进行测试...">客户：张三 +60123456789
接送：KLIA2机场 到 吉隆坡双子塔
时间：2024-03-15 14:30
人数：3人
要求：需要儿童座椅</textarea>
        <br>
        <button onclick="testSmartParsing()">测试智能解析</button>
        <div id="parsingResult"></div>
    </div>

    <div class="test-container">
        <h2>ID映射测试</h2>
        <button onclick="testIdMapping()">测试ID映射功能</button>
        <div id="mappingResult"></div>
    </div>

    <script type="module">
        import geminiService from './js/gemini-service.js';
        import apiService from './js/api-service.js';
        import logger from './js/logger.js';

        // 暴露到全局作用域供按钮调用
        window.geminiService = geminiService;
        window.apiService = apiService;
        window.logger = logger;

        // 测试API连接
        window.testApiConnection = async function() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试API连接...</div>';

            try {
                const isAvailable = geminiService.isAvailable();
                const apiKey = geminiService.getApiKey();
                
                if (isAvailable && apiKey) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ API配置正常<br>
                            模型: ${geminiService.modelVersion}<br>
                            密钥长度: ${apiKey.length}字符
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ API配置异常</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        };

        // 测试智能解析
        window.testSmartParsing = async function() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('parsingResult');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入测试内容</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">正在进行智能解析...</div>';

            try {
                const result = await geminiService.parseOrder(input);
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 解析成功 (置信度: ${Math.round(result.confidence * 100)}%)<br>
                            <strong>提取的数据:</strong><br>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 解析失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 解析错误: ${error.message}</div>`;
            }
        };

        // 测试ID映射
        window.testIdMapping = function() {
            const resultDiv = document.getElementById('mappingResult');
            
            try {
                const allowedSubCategories = apiService.getAllowedSubCategories();
                const testData = {
                    service_type: 'pickup',
                    passenger_number: 3,
                    pickup: 'KLIA2机场',
                    destination: '吉隆坡',
                    extra_requirement: '需要中文司机'
                };
                
                const processedData = geminiService.performSmartIdFilling(testData);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ ID映射测试成功<br>
                        <strong>允许的子分类:</strong><br>
                        ${allowedSubCategories.map(cat => `${cat.id}: ${cat.name}`).join('<br>')}<br><br>
                        <strong>测试数据映射结果:</strong><br>
                        <pre>${JSON.stringify(processedData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 映射测试失败: ${error.message}</div>`;
            }
        };

        // 页面加载时显示配置状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Gemini配置测试页面已加载');
            console.log('模型版本:', geminiService.modelVersion);
            console.log('API可用性:', geminiService.isAvailable());
        });
    </script>
</body>
</html>
