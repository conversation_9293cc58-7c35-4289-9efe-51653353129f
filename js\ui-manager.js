/**
 * UI管理器模块
 * 负责管理用户界面的交互、状态更新、事件处理等
 * 支持实时分析功能
 */

import appState from './app-state.js';
import apiService from './api-service.js';
import geminiService from './gemini-service.js';
import logger from './logger.js';
import { formatDate, formatTime, debounce } from './utils.js';

class UIManager {
    constructor() {
        this.elements = {};
        this.isInitialized = false;
        this.currentModal = null;
        
        // 实时分析相关
        this.realtimeAnalysis = {
            enabled: true,
            debounceTimer: null,
            lastAnalysisTime: 0,
            isAnalyzing: false,
            progressIndicator: null
        };
        
        // 绑定方法上下文
        this.handleLogin = this.handleLogin.bind(this);
        this.handleLogout = this.handleLogout.bind(this);
        this.handleParseOrder = this.handleParseOrder.bind(this);
        this.handleCreateOrder = this.handleCreateOrder.bind(this);
        this.handleThemeToggle = this.handleThemeToggle.bind(this);
        this.handleRealtimeInput = this.handleRealtimeInput.bind(this);
    }
    
    /**
     * 初始化UI管理器
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.setupStateListeners();
        this.initializeTheme();
        this.setupRealtimeAnalysis();
        this.updateUI();
        
        this.isInitialized = true;
        logger.log('UI管理器初始化完成', 'success');
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            // 主要容器
            loginPanel: document.getElementById('loginPanel'),
            workspace: document.getElementById('workspace'),
            
            // 登录相关
            loginForm: document.getElementById('loginForm'),
            emailInput: document.getElementById('email'),
            passwordInput: document.getElementById('password'),
            loginBtn: document.getElementById('loginBtn'),
            
            // 用户信息
            userInfo: document.getElementById('userInfo'),
            currentUser: document.getElementById('currentUser'),
            logoutBtn: document.getElementById('logoutBtn'),
            
            // 智能输入
            orderInput: document.getElementById('orderInput'),
            parseBtn: document.getElementById('parseBtn'),
            clearInput: document.getElementById('clearInput'),
            sampleInput: document.getElementById('sampleInput'),
            geminiStatus: document.getElementById('geminiStatus'),
            
            // 订单表单
            orderForm: document.getElementById('orderForm'),
            createOrder: document.getElementById('createOrder'),
            previewOrder: document.getElementById('previewOrder'),
            validateOrder: document.getElementById('validateOrder'),
            resetOrder: document.getElementById('resetOrder'),
            
            // 表单字段
            subCategoryId: document.getElementById('subCategoryId'),
            otaReferenceNumber: document.getElementById('otaReferenceNumber'),
            carTypeId: document.getElementById('carTypeId'),
            inchargeByBackendUserId: document.getElementById('inchargeByBackendUserId'),
            customerName: document.getElementById('customerName'),
            customerContact: document.getElementById('customerContact'),
            customerEmail: document.getElementById('customerEmail'),
            flightInfo: document.getElementById('flightInfo'),
            pickup: document.getElementById('pickup'),
            destination: document.getElementById('destination'),
            date: document.getElementById('date'),
            time: document.getElementById('time'),
            passengerNumber: document.getElementById('passengerNumber'),
            luggageNumber: document.getElementById('luggageNumber'),
            drivingRegionId: document.getElementById('drivingRegionId'),
            languagesIdArray: document.getElementById('languagesIdArray'),
            tourGuide: document.getElementById('tourGuide'),
            babyChair: document.getElementById('babyChair'),
            meetAndGreet: document.getElementById('meetAndGreet'),
            otaPrice: document.getElementById('otaPrice'),
            driverFee: document.getElementById('driverFee'),
            driverCollect: document.getElementById('driverCollect'),
            extraRequirement: document.getElementById('extraRequirement'),
            
            // 日志控制台
            logConsole: document.getElementById('logConsole'),
            clearLogs: document.getElementById('clearLogs'),
            exportLogs: document.getElementById('exportLogs'),
            debugMode: document.getElementById('debugMode'),
            
            // 状态栏
            connectionStatus: document.getElementById('connectionStatus'),
            dataStatus: document.getElementById('dataStatus'),
            lastUpdate: document.getElementById('lastUpdate'),
            
            // 主题切换
            themeToggle: document.getElementById('themeToggle'),
            
            // 模态框
            modal: document.getElementById('modal'),
            modalTitle: document.getElementById('modalTitle'),
            modalBody: document.getElementById('modalBody'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConfirm: document.getElementById('modalConfirm')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登录表单
        this.elements.loginForm?.addEventListener('submit', this.handleLogin);
        this.elements.logoutBtn?.addEventListener('click', this.handleLogout);
        
        // 智能输入 - 实时分析
        this.elements.orderInput?.addEventListener('input', this.handleRealtimeInput);
        this.elements.orderInput?.addEventListener('paste', this.handleRealtimeInput);
        
        // 保留手动解析按钮作为备用
        this.elements.parseBtn?.addEventListener('click', this.handleParseOrder);
        this.elements.clearInput?.addEventListener('click', () => {
            this.elements.orderInput.value = '';
            this.clearRealtimeAnalysis();
            this.updateGeminiStatus('请输入订单描述');
        });
        this.elements.sampleInput?.addEventListener('click', () => {
            this.elements.orderInput.value = geminiService.generateSampleOrder();
            this.triggerRealtimeAnalysis();
            this.updateGeminiStatus('示例数据已填入，正在自动分析...');
        });
        
        // 订单表单
        this.elements.orderForm?.addEventListener('submit', this.handleCreateOrder);
        this.elements.previewOrder?.addEventListener('click', this.handlePreviewOrder.bind(this));
        this.elements.validateOrder?.addEventListener('click', this.handleValidateOrder.bind(this));
        this.elements.resetOrder?.addEventListener('click', this.handleResetOrder.bind(this));
        
        // 乘客人数变化时自动推荐车型
        this.elements.passengerNumber?.addEventListener('change', this.handlePassengerChange.bind(this));
        
        // 日志控制台
        this.elements.clearLogs?.addEventListener('click', this.handleClearLogs.bind(this));
        this.elements.exportLogs?.addEventListener('click', this.handleExportLogs.bind(this));
        this.elements.debugMode?.addEventListener('change', this.handleDebugModeChange.bind(this));
        
        // 主题切换
        this.elements.themeToggle?.addEventListener('click', this.handleThemeToggle);
        
        // 模态框
        this.elements.modalClose?.addEventListener('click', this.hideModal.bind(this));
        this.elements.modalCancel?.addEventListener('click', this.hideModal.bind(this));
        this.elements.modal?.addEventListener('click', (e) => {
            if (e.target === this.elements.modal) {
                this.hideModal();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // 实时输入验证
        this.setupRealtimeValidation();
    }
    
    /**
     * 设置实时分析功能
     */
    setupRealtimeAnalysis() {
        // 配置实时分析参数
        geminiService.configureRealtimeAnalysis({
            debounceDelay: 2000, // 2秒防抖
            minInputLength: 15,  // 最小15字符
            confidenceThreshold: 0.2
        });
        
        // 启用实时分析
        geminiService.setRealtimeAnalysis(true);
        
        // 创建进度指示器
        this.createProgressIndicator();
        
        logger.log('实时分析功能已设置', 'info');
    }
    
    /**
     * 创建进度指示器
     */
    createProgressIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'realtime-progress';
        indicator.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">正在分析...</div>
        `;
        
        indicator.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            padding: var(--spacing-3);
            display: none;
            z-index: 10;
        `;
        
        const progressBarStyle = `
            .realtime-progress .progress-bar {
                width: 100%;
                height: 4px;
                background: var(--color-gray-200);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: var(--spacing-2);
            }
            .realtime-progress .progress-fill {
                height: 100%;
                background: var(--color-primary);
                border-radius: 2px;
                transition: width 0.3s ease;
                width: 0%;
            }
            .realtime-progress .progress-text {
                font-size: var(--font-size-xs);
                color: var(--text-secondary);
                text-align: center;
            }
        `;
        
        // 添加样式
        if (!document.getElementById('realtime-progress-styles')) {
            const style = document.createElement('style');
            style.id = 'realtime-progress-styles';
            style.textContent = progressBarStyle;
            document.head.appendChild(style);
        }
        
        // 将指示器添加到输入框容器
        const inputContainer = this.elements.orderInput?.parentElement;
        if (inputContainer) {
            inputContainer.style.position = 'relative';
            inputContainer.appendChild(indicator);
            this.realtimeAnalysis.progressIndicator = indicator;
        }
    }
    
    /**
     * 处理实时输入
     * @param {Event} event - 输入事件
     */
    handleRealtimeInput(event) {
        const inputText = event.target.value;
        
        // 清除之前的定时器
        if (this.realtimeAnalysis.debounceTimer) {
            clearTimeout(this.realtimeAnalysis.debounceTimer);
        }
        
        // 如果输入为空，清除分析状态
        if (!inputText.trim()) {
            this.clearRealtimeAnalysis();
            this.updateGeminiStatus('请输入订单描述');
            return;
        }
        
        // 检查输入长度
        if (inputText.trim().length < 15) {
            this.updateGeminiStatus(`请继续输入... (${inputText.trim().length}/15)`);
            return;
        }
        
        // 设置防抖定时器
        this.realtimeAnalysis.debounceTimer = setTimeout(() => {
            this.triggerRealtimeAnalysis();
        }, 2000);
        
        // 更新状态提示
        this.updateGeminiStatus('输入检测中，即将开始分析...');
    }
    
    /**
     * 触发实时分析
     */
    async triggerRealtimeAnalysis() {
        const orderText = this.elements.orderInput?.value?.trim();
        
        if (!orderText || !geminiService.isAvailable()) {
            return;
        }
        
        // 防止重复分析
        if (this.realtimeAnalysis.isAnalyzing) {
            return;
        }
        
        this.realtimeAnalysis.isAnalyzing = true;
        this.realtimeAnalysis.lastAnalysisTime = Date.now();
        
        // 显示进度指示器
        this.showProgressIndicator();
        
        try {
            await geminiService.analyzeRealtime(
                orderText,
                this.handleAnalysisProgress.bind(this),
                this.handleAnalysisResult.bind(this),
                this.handleAnalysisError.bind(this)
            );
        } catch (error) {
            this.handleAnalysisError(error);
        }
    }
    
    /**
     * 处理分析进度
     * @param {string} message - 进度消息
     * @param {number} progress - 进度百分比
     */
    handleAnalysisProgress(message, progress) {
        this.updateGeminiStatus(message);
        this.updateProgressIndicator(progress);
    }
    
    /**
     * 处理分析结果
     * @param {object} result - 分析结果
     */
    handleAnalysisResult(result) {
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
        
        if (result.success) {
            // 更新应用状态
            appState.setCurrentOrder({
                rawInput: this.elements.orderInput.value,
                parsedData: result.data,
                formData: result.data,
                status: 'parsed'
            });
            
            // 填充表单
            this.fillFormFromData(result.data);
            
            const confidence = Math.round(result.confidence * 100);
            this.updateGeminiStatus(`✅ 自动解析完成！置信度: ${confidence}%`);
            
            // 显示简短的成功提示
            this.showQuickToast(`AI自动解析成功 (${confidence}%)`, 'success');
            
        } else if (result.fallback && Object.keys(result.data).length > 0) {
            // 降级解析
            this.fillFormFromData(result.data);
            this.updateGeminiStatus('⚠️ 使用基础解析模式');
            this.showQuickToast('使用基础解析模式', 'warning');
        } else {
            this.updateGeminiStatus(`❌ 解析失败: ${result.error}`);
        }
        
        logger.logGeminiInteraction(
            this.elements.orderInput.value,
            result,
            result.confidence
        );
    }
    
    /**
     * 处理分析错误
     * @param {Error} error - 错误对象
     */
    handleAnalysisError(error) {
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
        
        this.updateGeminiStatus(`❌ 分析错误: ${error.message}`);
        logger.logError('实时分析失败', error);
    }
    
    /**
     * 显示进度指示器
     */
    showProgressIndicator() {
        if (this.realtimeAnalysis.progressIndicator) {
            this.realtimeAnalysis.progressIndicator.style.display = 'block';
        }
    }
    
    /**
     * 隐藏进度指示器
     */
    hideProgressIndicator() {
        if (this.realtimeAnalysis.progressIndicator) {
            this.realtimeAnalysis.progressIndicator.style.display = 'none';
        }
    }
    
    /**
     * 更新进度指示器
     * @param {number} progress - 进度百分比
     */
    updateProgressIndicator(progress) {
        if (this.realtimeAnalysis.progressIndicator) {
            const progressFill = this.realtimeAnalysis.progressIndicator.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${Math.min(progress, 100)}%`;
            }
        }
    }
    
    /**
     * 清除实时分析状态
     */
    clearRealtimeAnalysis() {
        // 清除定时器
        if (this.realtimeAnalysis.debounceTimer) {
            clearTimeout(this.realtimeAnalysis.debounceTimer);
            this.realtimeAnalysis.debounceTimer = null;
        }
        
        // 取消当前分析
        geminiService.cancelCurrentAnalysis();
        
        // 重置状态
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
    }
    
    /**
     * 显示快速提示
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型
     */
    showQuickToast(message, type = 'info') {
        // 创建简单的提示元素
        const toast = document.createElement('div');
        toast.className = `quick-toast ${type}`;
        toast.textContent = message;
        
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--color-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'});
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }
    
    /**
     * 设置状态监听器
     */
    setupStateListeners() {
        // 监听登录状态变化
        appState.on('auth.isLoggedIn', (isLoggedIn) => {
            this.updateLoginUI(isLoggedIn);
        });
        
        // 监听系统数据变化
        appState.on('systemData.lastUpdated', () => {
            this.populateFormOptions();
            this.updateDataStatus();
        });
        
        // 监听当前订单变化
        appState.on('currentOrder', () => {
            this.updateOrderForm();
        });
        
        // 监听主题变化
        appState.on('config.theme', (theme) => {
            document.documentElement.setAttribute('data-theme', theme);
            this.updateThemeIcon(theme);
        });
        
        // 监听连接状态变化
        appState.on('system.connected', (connected) => {
            this.updateConnectionStatus(connected);
        });
    }
    
    /**
     * 初始化主题
     */
    initializeTheme() {
        const theme = appState.get('config.theme') || 'light';
        document.documentElement.setAttribute('data-theme', theme);
        this.updateThemeIcon(theme);
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        const isLoggedIn = appState.get('auth.isLoggedIn');
        this.updateLoginUI(isLoggedIn);
        
        if (isLoggedIn) {
            this.populateFormOptions();
            this.updateDataStatus();
        }
        
        this.updateConnectionStatus(appState.get('system.connected'));
        this.updateLastUpdateTime();
        this.updateGeminiStatus();
    }
    
    /**
     * 处理登录
     * @param {Event} e - 表单提交事件
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const email = this.elements.emailInput.value.trim();
        const password = this.elements.passwordInput.value;
        
        if (!email || !password) {
            this.showAlert('请输入邮箱和密码', 'warning');
            return;
        }
        
        this.setButtonLoading(this.elements.loginBtn, true);
        
        try {
            await apiService.login(email, password);
            
            // 登录成功后获取系统数据
            try {
                await apiService.getAllSystemData();
                this.showAlert('登录成功！', 'success');
            } catch (error) {
                logger.log('获取系统数据失败，使用静态数据', 'warning', { error: error.message });
                this.showAlert('登录成功，但系统数据获取失败，将使用本地数据', 'warning');
            }
            
        } catch (error) {
            this.showAlert(`登录失败: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.elements.loginBtn, false);
        }
    }
    
    /**
     * 处理登出
     */
    handleLogout() {
        this.showConfirm(
            '确认登出',
            '确定要退出登录吗？当前未保存的数据可能丢失。',
            () => {
                appState.clearAuth();
                appState.clearCurrentOrder();
                this.clearRealtimeAnalysis();
                this.showAlert('已退出登录', 'info');
            }
        );
    }
    
    /**
     * 处理手动订单解析（保留作为备用功能）
     */
    async handleParseOrder() {
        const orderText = this.elements.orderInput.value.trim();
        
        if (!orderText) {
            this.showAlert('请输入订单描述', 'warning');
            return;
        }
        
        if (!geminiService.isAvailable()) {
            this.showApiKeyPrompt();
            return;
        }
        
        // 停止实时分析
        this.clearRealtimeAnalysis();
        
        this.setButtonLoading(this.elements.parseBtn, true);
        this.updateGeminiStatus('手动解析中...');
        
        try {
            const result = await geminiService.parseOrder(orderText);
            
            if (result.success) {
                // 更新应用状态
                appState.setCurrentOrder({
                    rawInput: orderText,
                    parsedData: result.data,
                    formData: result.data,
                    status: 'parsed'
                });
                
                // 填充表单
                this.fillFormFromData(result.data);
                
                const confidence = Math.round(result.confidence * 100);
                this.updateGeminiStatus(`手动解析完成！置信度: ${confidence}%`);
                this.showAlert(`手动解析成功，置信度: ${confidence}%`, 'success');
                
            } else {
                this.updateGeminiStatus(`解析失败: ${result.error}`);
                this.showAlert(`解析失败: ${result.error}`, 'error');
            }
            
        } catch (error) {
            this.updateGeminiStatus(`解析错误: ${error.message}`);
            this.showAlert(`解析错误: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.elements.parseBtn, false);
        }
    }
    
    /**
     * 处理订单创建
     * @param {Event} e - 表单提交事件
     */
    async handleCreateOrder(e) {
        e.preventDefault();
        
        const orderData = this.collectFormData();
        const validation = apiService.validateOrderData(orderData);
        
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }
        
        if (validation.warnings.length > 0) {
            this.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
        }
        
        this.setButtonLoading(this.elements.createOrder, true);
        
        try {
            const result = await apiService.createOrder(orderData);
            
            if (result.success) {
                this.showAlert('订单创建成功！', 'success');
                this.showModal('订单创建成功', `
                    <div class="success-message">
                        <p><strong>订单已成功创建</strong></p>
                        <p>订单ID: ${result.data?.id || 'N/A'}</p>
                        <p>创建时间: ${formatDate(new Date())}</p>
                    </div>
                `);
                
                // 清空表单或保存为草稿
                this.handleResetOrder();
                
            } else {
                this.showValidationErrors(result.errors);
                this.showAlert(`订单创建失败: ${result.message}`, 'error');
            }
            
        } catch (error) {
            this.showAlert(`订单创建异常: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.elements.createOrder, false);
        }
    }
    
    /**
     * 处理订单预览
     */
    handlePreviewOrder() {
        const orderData = this.collectFormData();
        const validation = apiService.validateOrderData(orderData);
        
        let content = '<div class="order-preview">';
        content += '<h4>订单预览</h4>';
        
        // 基本信息
        content += '<div class="preview-section">';
        content += '<h5>基本信息</h5>';
        content += `<p><strong>子分类:</strong> ${this.getSubCategoryName(orderData.sub_category_id)}</p>`;
        content += `<p><strong>OTA参考号:</strong> ${orderData.ota_reference_number || 'N/A'}</p>`;
        content += `<p><strong>车型:</strong> ${this.getCarTypeName(orderData.car_type_id)}</p>`;
        content += `<p><strong>负责人:</strong> ${this.getBackendUserName(orderData.incharge_by_backend_user_id)}</p>`;
        content += '</div>';
        
        // 客户信息
        if (orderData.customer_name || orderData.customer_contact || orderData.customer_email) {
            content += '<div class="preview-section">';
            content += '<h5>客户信息</h5>';
            if (orderData.customer_name) content += `<p><strong>姓名:</strong> ${orderData.customer_name}</p>`;
            if (orderData.customer_contact) content += `<p><strong>电话:</strong> ${orderData.customer_contact}</p>`;
            if (orderData.customer_email) content += `<p><strong>邮箱:</strong> ${orderData.customer_email}</p>`;
            content += '</div>';
        }
        
        // 行程信息
        content += '<div class="preview-section">';
        content += '<h5>行程信息</h5>';
        content += `<p><strong>上车地点:</strong> ${orderData.pickup || 'N/A'}</p>`;
        content += `<p><strong>目的地:</strong> ${orderData.destination || 'N/A'}</p>`;
        content += `<p><strong>日期:</strong> ${orderData.date || 'N/A'}</p>`;
        content += `<p><strong>时间:</strong> ${orderData.time || 'N/A'}</p>`;
        content += '</div>';
        
        // 验证结果
        if (!validation.isValid) {
            content += '<div class="preview-section error">';
            content += '<h5>验证错误</h5>';
            Object.entries(validation.errors).forEach(([field, errors]) => {
                content += `<p><strong>${field}:</strong> ${errors.join(', ')}</p>`;
            });
            content += '</div>';
        }
        
        if (validation.warnings.length > 0) {
            content += '<div class="preview-section warning">';
            content += '<h5>警告</h5>';
            validation.warnings.forEach(warning => {
                content += `<p>${warning}</p>`;
            });
            content += '</div>';
        }
        
        content += '</div>';
        
        this.showModal('订单预览', content);
    }
    
    /**
     * 处理订单验证
     */
    handleValidateOrder() {
        const orderData = this.collectFormData();
        const validation = apiService.validateOrderData(orderData);
        
        if (validation.isValid) {
            this.showAlert('数据验证通过！', 'success');
        } else {
            this.showValidationErrors(validation.errors);
        }
        
        if (validation.warnings.length > 0) {
            this.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
        }
    }
    
    /**
     * 处理订单重置
     */
    handleResetOrder() {
        this.showConfirm(
            '重置表单',
            '确定要重置所有表单数据吗？',
            () => {
                this.elements.orderForm.reset();
                this.elements.orderInput.value = '';
                this.clearRealtimeAnalysis();
                appState.clearCurrentOrder();
                this.updateGeminiStatus('请输入订单描述');
                this.showAlert('表单已重置', 'info');
            }
        );
    }
    
    /**
     * 处理乘客人数变化
     */
    handlePassengerChange() {
        const passengerCount = parseInt(this.elements.passengerNumber.value);
        if (!isNaN(passengerCount) && passengerCount > 0) {
            const recommendedCarType = apiService.recommendCarType(passengerCount);
            this.elements.carTypeId.value = recommendedCarType;
            
            // 自动设置行李数量建议
            this.elements.luggageNumber.value = Math.min(passengerCount, 4);
        }
    }
    
    /**
     * 处理主题切换
     */
    handleThemeToggle() {
        const currentTheme = appState.get('config.theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        appState.setTheme(newTheme);
    }
    
    /**
     * 处理清空日志
     */
    handleClearLogs() {
        logger.clear();
        this.updateLogConsole();
    }
    
    /**
     * 处理导出日志
     */
    handleExportLogs() {
        const logs = logger.export();
        const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ota-system-logs-${formatDate(new Date())}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showAlert('日志已导出', 'success');
    }
    
    /**
     * 处理调试模式变化
     */
    handleDebugModeChange() {
        const enabled = this.elements.debugMode.checked;
        appState.setDebugMode(enabled);
        logger.setDebugMode(enabled);
        
        this.showAlert(`调试模式已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter: 手动解析订单
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (document.activeElement === this.elements.orderInput) {
                e.preventDefault();
                this.handleParseOrder();
            }
        }
        
        // Escape: 关闭模态框
        if (e.key === 'Escape' && this.currentModal) {
            this.hideModal();
        }
        
        // Ctrl/Cmd + Shift + R: 重置表单
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'R') {
            e.preventDefault();
            this.handleResetOrder();
        }
    }
    
    /**
     * 设置实时输入验证
     */
    setupRealtimeValidation() {
        // 邮箱验证
        if (this.elements.customerEmail) {
            this.elements.customerEmail.addEventListener('blur', () => {
                const email = this.elements.customerEmail.value;
                if (email && !apiService.isValidEmail(email)) {
                    this.showFieldError('customerEmail', '邮箱格式不正确');
                } else {
                    this.clearFieldError('customerEmail');
                }
            });
        }
        
        // 电话验证
        if (this.elements.customerContact) {
            this.elements.customerContact.addEventListener('blur', () => {
                const phone = this.elements.customerContact.value;
                if (phone && !apiService.isValidPhone(phone)) {
                    this.showFieldError('customerContact', '电话格式可能不正确');
                } else {
                    this.clearFieldError('customerContact');
                }
            });
        }
        
        // 日期验证
        if (this.elements.date) {
            this.elements.date.addEventListener('change', () => {
                const date = this.elements.date.value;
                if (date) {
                    const selectedDate = new Date(date);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    
                    if (selectedDate < today) {
                        this.showFieldError('date', '不能选择过去的日期');
                    } else {
                        this.clearFieldError('date');
                    }
                }
            });
        }
    }
    
    /**
     * 更新登录UI状态
     * @param {boolean} isLoggedIn - 是否已登录
     */
    updateLoginUI(isLoggedIn) {
        if (isLoggedIn) {
            this.elements.loginPanel.style.display = 'none';
            this.elements.workspace.style.display = 'block';
            this.elements.userInfo.style.display = 'flex';
            
            const userEmail = appState.get('auth.user.email');
            this.elements.currentUser.textContent = userEmail || '已登录';
            
        } else {
            this.elements.loginPanel.style.display = 'flex';
            this.elements.workspace.style.display = 'none';
            this.elements.userInfo.style.display = 'none';
        }
    }
    
    /**
     * 填充表单选项（优化版本，限制子分类范围）
     */
    populateFormOptions() {
        // 填充后台用户
        const backendUsers = appState.get('systemData.backendUsers') || [];
        this.populateSelect(this.elements.inchargeByBackendUserId, backendUsers, 'id', 'name');

        // 设置默认后台用户
        const defaultUserId = appState.getDefaultBackendUser();
        if (defaultUserId) {
            this.elements.inchargeByBackendUserId.value = defaultUserId;
        }

        // 填充子分类（仅限制的三种服务类型）
        const allowedSubCategories = apiService.getAllowedSubCategories();
        this.populateSelect(this.elements.subCategoryId, allowedSubCategories, 'id', 'name');

        // 添加子分类描述提示
        this.addSubCategoryTooltips();

        // 填充车型
        const carTypes = appState.get('systemData.carTypes') || [];
        this.populateSelect(this.elements.carTypeId, carTypes, 'id', 'name');

        // 填充行驶区域
        const drivingRegions = appState.get('systemData.drivingRegions') || [];
        this.populateSelect(this.elements.drivingRegionId, drivingRegions, 'id', 'name');

        // 填充语言（多选）
        const languages = appState.get('systemData.languages') || [];
        this.populateSelect(this.elements.languagesIdArray, languages, 'id', 'name');

        logger.log('表单选项填充完成', 'info', {
            allowedSubCategories: allowedSubCategories.length,
            backendUsers: backendUsers.length,
            carTypes: carTypes.length
        });
    }

    /**
     * 添加子分类选择提示
     */
    addSubCategoryTooltips() {
        if (this.elements.subCategoryId) {
            const label = this.elements.subCategoryId.parentElement.querySelector('label');
            if (label) {
                label.title = '仅支持：接机(Pickup)、送机(Dropoff)、包车(Charter)三种服务类型';
                label.style.cursor = 'help';
            }
        }
    }
    
    /**
     * 填充下拉选择框
     * @param {HTMLSelectElement} selectElement - 选择框元素
     * @param {Array} options - 选项数组
     * @param {string} valueField - 值字段名
     * @param {string} textField - 显示文本字段名
     */
    populateSelect(selectElement, options, valueField, textField) {
        if (!selectElement || !Array.isArray(options)) return;
        
        // 保持默认选项
        const defaultOptions = Array.from(selectElement.children).filter(option => option.value === '');
        
        // 清空现有选项（除默认选项外）
        selectElement.innerHTML = '';
        defaultOptions.forEach(option => selectElement.appendChild(option));
        
        // 添加新选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option[valueField];
            optionElement.textContent = option[textField];
            selectElement.appendChild(optionElement);
        });
    }
    
    /**
     * 从解析数据填充表单
     * @param {object} data - 解析后的数据
     */
    fillFormFromData(data) {
        // 基本字段映射
        const fieldMapping = {
            customer_name: 'customerName',
            customer_contact: 'customerContact',
            customer_email: 'customerEmail',
            flight_info: 'flightInfo',
            pickup: 'pickup',
            destination: 'destination',
            date: 'date',
            time: 'time',
            passenger_number: 'passengerNumber',
            luggage_number: 'luggageNumber',
            ota_price: 'otaPrice',
            driver_fee: 'driverFee',
            driver_collect: 'driverCollect',
            extra_requirement: 'extraRequirement'
        };
        
        // 填充基本字段
        Object.entries(fieldMapping).forEach(([dataKey, elementKey]) => {
            if (data[dataKey] !== undefined && this.elements[elementKey]) {
                this.elements[elementKey].value = data[dataKey];
            }
        });
        
        // 处理特殊字段（智能ID填充优化）
        if (data.sub_category_id && this.elements.subCategoryId) {
            // 验证子分类ID是否在允许范围内
            if (apiService.isAllowedSubCategory(data.sub_category_id)) {
                this.elements.subCategoryId.value = data.sub_category_id;
                logger.log('子分类ID已填充', 'info', { subCategoryId: data.sub_category_id });
            } else {
                logger.log('子分类ID不在允许范围内，使用默认值', 'warning', {
                    subCategoryId: data.sub_category_id
                });
                this.elements.subCategoryId.value = 2; // 默认接机服务
            }
        }

        if (data.car_type_id && this.elements.carTypeId) {
            this.elements.carTypeId.value = data.car_type_id;
            logger.log('车型ID已填充', 'info', { carTypeId: data.car_type_id });
        } else if (data.passenger_number) {
            // 根据乘客人数推荐车型
            const recommendedCarType = apiService.recommendCarType(data.passenger_number);
            this.elements.carTypeId.value = recommendedCarType;
            logger.log('根据乘客人数推荐车型', 'info', {
                passengerNumber: data.passenger_number,
                recommendedCarType
            });
        }

        if (data.driving_region_id && this.elements.drivingRegionId) {
            this.elements.drivingRegionId.value = data.driving_region_id;
            logger.log('行驶区域ID已填充', 'info', { drivingRegionId: data.driving_region_id });
        }

        // 处理后台用户ID
        if (data.incharge_by_backend_user_id && this.elements.inchargeByBackendUserId) {
            this.elements.inchargeByBackendUserId.value = data.incharge_by_backend_user_id;
            logger.log('后台用户ID已填充', 'info', {
                backendUserId: data.incharge_by_backend_user_id
            });
        }
        
        // 处理复选框
        if (data.baby_chair !== undefined) {
            this.elements.babyChair.checked = Boolean(data.baby_chair);
        }
        if (data.tour_guide !== undefined) {
            this.elements.tourGuide.checked = Boolean(data.tour_guide);
        }
        if (data.meet_and_greet !== undefined) {
            this.elements.meetAndGreet.checked = Boolean(data.meet_and_greet);
        }
        
        // 处理语言数组（支持对象格式）
        if (data.languages_id_array) {
            // 清除现有选择
            Array.from(this.elements.languagesIdArray.options).forEach(option => {
                option.selected = false;
            });

            let languageIds = [];

            // 处理不同格式的语言数组
            if (Array.isArray(data.languages_id_array)) {
                languageIds = data.languages_id_array;
            } else if (typeof data.languages_id_array === 'object') {
                // 对象格式：{"0":"2","1":"4"}
                languageIds = Object.values(data.languages_id_array).map(id => parseInt(id));
            }

            // 设置新选择
            languageIds.forEach(langId => {
                const option = this.elements.languagesIdArray.querySelector(`option[value="${langId}"]`);
                if (option) {
                    option.selected = true;
                }
            });

            logger.log('语言选择已填充', 'info', {
                languageIds,
                format: Array.isArray(data.languages_id_array) ? 'array' : 'object'
            });
        }
        
        // 生成OTA参考号（如果没有）
        if (!data.ota_reference_number && this.elements.otaReferenceNumber) {
            const timestamp = Date.now().toString().slice(-6);
            this.elements.otaReferenceNumber.value = `GMH-${timestamp}`;
        }
        
        // 触发乘客人数变化事件
        if (data.passenger_number) {
            this.handlePassengerChange();
        }
    }
    
    /**
     * 收集表单数据
     * @returns {object} 表单数据
     */
    collectFormData() {
        const data = {};
        
        // 基本字段
        const fields = [
            'subCategoryId', 'otaReferenceNumber', 'carTypeId', 'inchargeByBackendUserId',
            'customerName', 'customerContact', 'customerEmail', 'flightInfo',
            'pickup', 'destination', 'date', 'time',
            'passengerNumber', 'luggageNumber', 'drivingRegionId',
            'otaPrice', 'driverFee', 'driverCollect', 'extraRequirement'
        ];
        
        fields.forEach(field => {
            const element = this.elements[field];
            if (element && element.value.trim()) {
                const apiField = this.camelToSnake(field);
                data[apiField] = element.value.trim();
            }
        });
        
        // 复选框字段
        if (this.elements.tourGuide.checked) data.tour_guide = true;
        if (this.elements.babyChair.checked) data.baby_chair = true;
        if (this.elements.meetAndGreet.checked) data.meet_and_greet = true;
        
        // 语言数组（转换为对象格式以确保GoMyHire API兼容性）
        const selectedLanguages = Array.from(this.elements.languagesIdArray.selectedOptions)
            .map(option => parseInt(option.value))
            .filter(value => !isNaN(value));

        if (selectedLanguages.length > 0) {
            // 转换为对象格式：{"0":"2","1":"4"}
            const languagesObject = {};
            selectedLanguages.forEach((id, index) => {
                languagesObject[index.toString()] = id.toString();
            });
            data.languages_id_array = languagesObject;

            logger.log('语言数组已转换为对象格式', 'info', {
                selectedLanguages,
                languagesObject
            });
        }
        
        return data;
    }
    
    /**
     * 将驼峰命名转换为下划线命名
     * @param {string} str - 驼峰命名字符串
     * @returns {string} 下划线命名字符串
     */
    camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }
    
    /**
     * 显示验证错误
     * @param {object} errors - 错误对象
     */
    showValidationErrors(errors) {
        let errorMessages = [];
        Object.entries(errors).forEach(([field, messages]) => {
            errorMessages.push(`${field}: ${messages.join(', ')}`);
            this.showFieldError(field, messages.join(', '));
        });
        
        this.showAlert(`验证失败:\n${errorMessages.join('\n')}`, 'error');
    }
    
    /**
     * 显示字段错误
     * @param {string} fieldName - 字段名
     * @param {string} message - 错误消息
     */
    showFieldError(fieldName, message) {
        const element = this.elements[fieldName];
        if (!element) return;
        
        element.classList.add('error');
        
        // 移除现有错误消息
        const existingError = element.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // 添加新错误消息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        element.parentNode.appendChild(errorDiv);
    }
    
    /**
     * 清除字段错误
     * @param {string} fieldName - 字段名
     */
    clearFieldError(fieldName) {
        const element = this.elements[fieldName];
        if (!element) return;
        
        element.classList.remove('error');
        
        const errorDiv = element.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    /**
     * 设置按钮加载状态
     * @param {HTMLButtonElement} button - 按钮元素
     * @param {boolean} loading - 是否加载中
     */
    setButtonLoading(button, loading) {
        if (!button) return;
        
        const textSpan = button.querySelector('.btn-text');
        const spinner = button.querySelector('.loading-spinner');
        
        if (loading) {
            button.disabled = true;
            if (textSpan) textSpan.style.display = 'none';
            if (spinner) spinner.style.display = 'inline';
        } else {
            button.disabled = false;
            if (textSpan) textSpan.style.display = 'inline';
            if (spinner) spinner.style.display = 'none';
        }
    }
    
    /**
     * 更新Gemini状态显示
     * @param {string} status - 状态文本
     */
    updateGeminiStatus(status = null) {
        if (!this.elements.geminiStatus) return;
        
        if (status) {
            this.elements.geminiStatus.textContent = status;
        } else {
            const available = geminiService.isAvailable();
            const realtimeEnabled = geminiService.realtimeConfig?.enabled;
            
            if (available && realtimeEnabled) {
                this.elements.geminiStatus.textContent = '🤖 AI实时分析已启用';
            } else if (available) {
                this.elements.geminiStatus.textContent = '🤖 AI助手已就绪';
            } else {
                this.elements.geminiStatus.textContent = '⚠️ 请设置Gemini API密钥';
            }
        }
    }
    
    /**
     * 更新数据状态
     */
    updateDataStatus() {
        const hasData = appState.get('systemData.lastUpdated') !== null;
        const status = hasData ? '📊 数据已就绪' : '📊 等待数据';
        this.elements.dataStatus.textContent = status;
    }
    
    /**
     * 更新连接状态
     * @param {boolean} connected - 是否已连接
     */
    updateConnectionStatus(connected) {
        const status = connected ? '🔌 已连接' : '🔌 未连接';
        this.elements.connectionStatus.textContent = status;
    }
    
    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const lastUpdate = appState.get('systemData.lastUpdated');
        if (lastUpdate) {
            const time = formatTime(new Date(lastUpdate));
            this.elements.lastUpdate.textContent = `⏰ ${time}`;
        } else {
            this.elements.lastUpdate.textContent = '⏰ --:--';
        }
    }
    
    /**
     * 更新主题图标
     * @param {string} theme - 主题名称
     */
    updateThemeIcon(theme) {
        if (this.elements.themeToggle) {
            this.elements.themeToggle.textContent = theme === 'light' ? '🌙' : '☀️';
            this.elements.themeToggle.title = theme === 'light' ? '切换到暗色主题' : '切换到亮色主题';
        }
    }
    
    /**
     * 更新日志控制台
     */
    updateLogConsole() {
        if (!this.elements.logConsole) return;
        
        const logs = logger.getLogs();
        const logHTML = logs.map(log => {
            const timestamp = formatTime(new Date(log.timestamp));
            return `<div class="log-entry ${log.level}">
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level">[${log.level.toUpperCase()}]</span>
                <span class="log-message">${log.message}</span>
                ${log.data ? `<div class="log-data">${JSON.stringify(log.data, null, 2)}</div>` : ''}
            </div>`;
        }).join('');
        
        this.elements.logConsole.innerHTML = logHTML;
        this.elements.logConsole.scrollTop = this.elements.logConsole.scrollHeight;
    }
    
    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {object} options - 选项
     */
    showModal(title, content, options = {}) {
        this.elements.modalTitle.textContent = title;
        this.elements.modalBody.innerHTML = content;
        
        // 配置按钮
        if (options.showCancel !== false) {
            this.elements.modalCancel.style.display = 'inline-flex';
        } else {
            this.elements.modalCancel.style.display = 'none';
        }
        
        if (options.confirmText) {
            this.elements.modalConfirm.textContent = options.confirmText;
        } else {
            this.elements.modalConfirm.textContent = '确认';
        }
        
        if (options.onConfirm) {
            this.elements.modalConfirm.onclick = () => {
                options.onConfirm();
                this.hideModal();
            };
        }
        
        this.elements.modal.style.display = 'flex';
        this.currentModal = this.elements.modal;
        
        // 自动关闭
        if (options.autoClose) {
            setTimeout(() => this.hideModal(), options.autoClose);
        }
    }
    
    /**
     * 隐藏模态框
     */
    hideModal() {
        if (this.elements.modal) {
            this.elements.modal.style.display = 'none';
            this.currentModal = null;
        }
    }
    
    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {function} onConfirm - 确认回调
     * @param {function} onCancel - 取消回调
     */
    showConfirm(title, message, onConfirm, onCancel = null) {
        this.showModal(title, `<p>${message}</p>`, {
            onConfirm,
            confirmText: '确认'
        });
        
        if (onCancel) {
            this.elements.modalCancel.onclick = () => {
                onCancel();
                this.hideModal();
            };
        }
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长（毫秒）
     */
    showAlert(message, type = 'info', duration = 5000) {
        // 创建提示元素
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <span class="alert-icon">${this.getAlertIcon(type)}</span>
            <span class="alert-message">${message}</span>
            <button class="alert-close">✕</button>
        `;
        
        // 添加样式
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-3);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 400px;
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                alert.style.borderLeftColor = 'var(--color-success)';
                break;
            case 'error':
                alert.style.borderLeftColor = 'var(--color-error)';
                break;
            case 'warning':
                alert.style.borderLeftColor = 'var(--color-warning)';
                break;
            default:
                alert.style.borderLeftColor = 'var(--color-info)';
        }
        
        // 添加到页面
        document.body.appendChild(alert);
        
        // 动画显示
        setTimeout(() => {
            alert.style.transform = 'translateX(0)';
        }, 10);
        
        // 关闭按钮事件
        const closeBtn = alert.querySelector('.alert-close');
        const closeAlert = () => {
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeAlert);
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(closeAlert, duration);
        }
        
        // 记录日志
        logger.log(message, type);
    }
    
    /**
     * 获取提示图标
     * @param {string} type - 提示类型
     * @returns {string} 图标
     */
    getAlertIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * 显示API密钥设置提示
     */
    showApiKeyPrompt() {
        const content = `
            <div class="api-key-prompt">
                <p>要使用AI解析功能，请输入Gemini API密钥：</p>
                <div class="form-group">
                    <label for="geminiApiKey">Gemini API Key:</label>
                    <input type="password" id="geminiApiKey" placeholder="输入您的Gemini API密钥">
                    <small>
                        获取API密钥: <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                    </small>
                </div>
            </div>
        `;
        
        this.showModal('设置Gemini API密钥', content, {
            confirmText: '保存',
            onConfirm: () => {
                const apiKey = document.getElementById('geminiApiKey').value.trim();
                if (apiKey) {
                    geminiService.setApiKey(apiKey);
                    this.updateGeminiStatus();
                    this.showAlert('API密钥已保存，实时分析已启用', 'success');
                } else {
                    this.showAlert('请输入有效的API密钥', 'warning');
                }
            }
        });
    }
    
    /**
     * 获取名称映射方法
     */
    getSubCategoryName(id) {
        const subCategories = appState.get('systemData.subCategories') || [];
        const category = subCategories.find(cat => cat.id == id);
        return category ? category.name : `子分类 ${id}`;
    }
    
    getCarTypeName(id) {
        const carTypes = appState.get('systemData.carTypes') || [];
        const carType = carTypes.find(car => car.id == id);
        return carType ? carType.name : `车型 ${id}`;
    }
    
    getBackendUserName(id) {
        const users = appState.get('systemData.backendUsers') || [];
        const user = users.find(u => u.id == id);
        return user ? user.name : `用户 ${id}`;
    }
}

// 创建全局UI管理器实例
const uiManager = new UIManager();

// 监听日志更新
logger.on((logEntry) => {
    if (uiManager.isInitialized) {
        uiManager.updateLogConsole();
    }
});

export default uiManager;