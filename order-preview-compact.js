/**
 * 紧凑型订单预览与编辑功能
 * 集成智能匹配和实时预览功能
 */

import appState from './js/app-state.js';
import apiService from './js/api-service.js';
import logger from './js/logger.js';
import utils from './js/utils.js';

class CompactOrderPreview {
    constructor() {
        this.isEditMode = false;
        this.currentOrderData = {};
        this.originalOrderData = {};
        this.hasUnsavedChanges = false;
        this.smartMatching = {
            enabled: true,
            confidence: {}
        };
        
        // 智能匹配规则
        this.matchingRules = {
            subCategory: {
                keywords: {
                    '接机': 2,
                    '机场接': 2,
                    'pickup': 2,
                    '送机': 3,
                    '机场送': 3,
                    'dropoff': 3,
                    '包车': 4,
                    'charter': 4
                }
            },
            region: {
                keywords: {
                    'KLIA': 1,
                    'KL': 1,
                    '吉隆坡': 1,
                    '雪兰莪': 1,
                    '槟城': 2,
                    'Penang': 2,
                    '柔佛': 3,
                    'Johor': 3,
                    '沙巴': 4,
                    'Sabah': 4
                }
            },
            language: {
                keywords: {
                    '中文': [4],
                    '英文': [2],
                    '马来': [3],
                    'Chinese': [4],
                    'English': [2],
                    'Malay': [3]
                }
            }
        };
        
        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.toggleEditMode = this.toggleEditMode.bind(this);
        this.saveChanges = this.saveChanges.bind(this);
        this.handleFieldChange = this.handleFieldChange.bind(this);
        this.performSmartMatching = this.performSmartMatching.bind(this);
    }
    
    /**
     * 初始化紧凑型订单预览
     */
    async init() {
        try {
            logger.logUserAction('紧凑型订单预览初始化');
            
            // 缓存DOM元素
            this.cacheElements();
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 加载订单数据
            await this.loadOrderData();
            
            // 初始化智能匹配
            this.initializeSmartMatching();
            
            // 初始化UI状态
            this.initializeUI();
            
            logger.logUserAction('紧凑型订单预览初始化完成');
            
        } catch (error) {
            logger.logError('紧凑型订单预览初始化失败', error);
            this.showNotification('初始化失败，请刷新页面重试', 'error');
        }
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            // 主容器
            app: document.getElementById('compactOrderApp'),
            
            // 头部控件
            backBtn: document.getElementById('backBtn'),
            editBtn: document.getElementById('editBtn'),
            saveBtn: document.getElementById('saveBtn'),
            resetBtn: document.getElementById('resetBtn'),
            
            // 状态显示
            orderId: document.getElementById('orderId'),
            orderStatus: document.getElementById('orderStatus'),
            
            // 智能指示器
            basicIndicator: document.getElementById('basicIndicator'),
            tripIndicator: document.getElementById('tripIndicator'),
            customerIndicator: document.getElementById('customerIndicator'),
            
            // 基本信息字段
            displayOtaRef: document.getElementById('displayOtaRef'),
            editOtaRef: document.getElementById('editOtaRef'),
            displaySubCategory: document.getElementById('displaySubCategory'),
            editSubCategory: document.getElementById('editSubCategory'),
            subCategoryHint: document.getElementById('subCategoryHint'),
            displayIncharge: document.getElementById('displayIncharge'),
            editIncharge: document.getElementById('editIncharge'),
            
            // 行程信息字段
            displayPickup: document.getElementById('displayPickup'),
            editPickup: document.getElementById('editPickup'),
            displayDestination: document.getElementById('displayDestination'),
            editDestination: document.getElementById('editDestination'),
            displayDate: document.getElementById('displayDate'),
            editDate: document.getElementById('editDate'),
            displayTime: document.getElementById('displayTime'),
            editTime: document.getElementById('editTime'),
            displayCarType: document.getElementById('displayCarType'),
            editCarType: document.getElementById('editCarType'),
            carTypeHint: document.getElementById('carTypeHint'),
            displayPassenger: document.getElementById('displayPassenger'),
            editPassenger: document.getElementById('editPassenger'),
            displayLuggage: document.getElementById('displayLuggage'),
            editLuggage: document.getElementById('editLuggage'),
            displayRegion: document.getElementById('displayRegion'),
            editRegion: document.getElementById('editRegion'),
            regionHint: document.getElementById('regionHint'),
            
            // 客户信息字段
            displayCustomerName: document.getElementById('displayCustomerName'),
            editCustomerName: document.getElementById('editCustomerName'),
            displayCustomerPhone: document.getElementById('displayCustomerPhone'),
            editCustomerPhone: document.getElementById('editCustomerPhone'),
            displayCustomerEmail: document.getElementById('displayCustomerEmail'),
            editCustomerEmail: document.getElementById('editCustomerEmail'),
            displayFlightInfo: document.getElementById('displayFlightInfo'),
            editFlightInfo: document.getElementById('editFlightInfo'),
            displayLanguages: document.getElementById('displayLanguages'),
            editLanguages: document.getElementById('editLanguages'),
            languageHint: document.getElementById('languageHint'),
            
            // 费用字段
            displayOtaPrice: document.getElementById('displayOtaPrice'),
            editOtaPrice: document.getElementById('editOtaPrice'),
            displayDriverFee: document.getElementById('displayDriverFee'),
            editDriverFee: document.getElementById('editDriverFee'),
            displayDriverCollect: document.getElementById('displayDriverCollect'),
            editDriverCollect: document.getElementById('editDriverCollect'),
            totalAmount: document.getElementById('totalAmount'),
            
            // 特殊要求
            babyChair: document.getElementById('babyChair'),
            tourGuide: document.getElementById('tourGuide'),
            meetGreet: document.getElementById('meetGreet'),
            displayExtraReq: document.getElementById('displayExtraReq'),
            editExtraReq: document.getElementById('editExtraReq'),
            
            // 底部操作
            validationStatus: document.getElementById('validationStatus'),
            previewBtn: document.getElementById('previewBtn'),
            validateBtn: document.getElementById('validateBtn'),
            cancelBtn: document.getElementById('cancelBtn'),
            confirmBtn: document.getElementById('confirmBtn'),
            
            // 智能提示和搜索
            smartTooltip: document.getElementById('smartTooltip'),
            tooltipBody: document.getElementById('tooltipBody'),
            quickSearch: document.getElementById('quickSearch'),
            searchInput: document.getElementById('searchInput'),
            searchResults: document.getElementById('searchResults'),
            
            // 加载指示器
            loadingOverlay: document.getElementById('loadingOverlay')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 头部按钮事件
        this.elements.backBtn?.addEventListener('click', this.handleBack.bind(this));
        this.elements.editBtn?.addEventListener('click', this.toggleEditMode);
        this.elements.saveBtn?.addEventListener('click', this.saveChanges);
        this.elements.resetBtn?.addEventListener('click', this.handleReset.bind(this));
        
        // 字段变更事件 - 基本信息
        this.bindFieldEvents('editOtaRef', 'ota_reference_number');
        this.bindFieldEvents('editSubCategory', 'sub_category_id');
        this.bindFieldEvents('editIncharge', 'incharge_by_backend_user_id');
        
        // 字段变更事件 - 行程信息
        this.bindFieldEvents('editPickup', 'pickup');
        this.bindFieldEvents('editDestination', 'destination');
        this.bindFieldEvents('editDate', 'date');
        this.bindFieldEvents('editTime', 'time');
        this.bindFieldEvents('editCarType', 'car_type_id');
        this.bindFieldEvents('editPassenger', 'passenger_number');
        this.bindFieldEvents('editLuggage', 'luggage_number');
        this.bindFieldEvents('editRegion', 'driving_region_id');
        
        // 字段变更事件 - 客户信息
        this.bindFieldEvents('editCustomerName', 'customer_name');
        this.bindFieldEvents('editCustomerPhone', 'customer_contact');
        this.bindFieldEvents('editCustomerEmail', 'customer_email');
        this.bindFieldEvents('editFlightInfo', 'flight_info');
        this.bindFieldEvents('editLanguages', 'languages_id_array');
        
        // 字段变更事件 - 费用信息
        this.bindFieldEvents('editOtaPrice', 'ota_price');
        this.bindFieldEvents('editDriverFee', 'driver_fee');
        this.bindFieldEvents('editDriverCollect', 'driver_collect');
        this.bindFieldEvents('editExtraReq', 'extra_requirement');
        
        // 复选框事件
        this.elements.babyChair?.addEventListener('change', (e) => {
            this.handleFieldChange('baby_chair', e.target.checked);
        });
        this.elements.tourGuide?.addEventListener('change', (e) => {
            this.handleFieldChange('tour_guide', e.target.checked);
        });
        this.elements.meetGreet?.addEventListener('change', (e) => {
            this.handleFieldChange('meet_and_greet', e.target.checked);
        });
        
        // 特殊事件 - 乘客人数变化触发车型推荐
        this.elements.editPassenger?.addEventListener('input', this.handlePassengerChange.bind(this));
        
        // 特殊事件 - 地点变化触发区域匹配
        this.elements.editPickup?.addEventListener('input', this.handleLocationChange.bind(this));
        this.elements.editDestination?.addEventListener('input', this.handleLocationChange.bind(this));
        
        // 底部操作按钮
        this.elements.previewBtn?.addEventListener('click', this.handlePreview.bind(this));
        this.elements.validateBtn?.addEventListener('click', this.handleValidate.bind(this));
        this.elements.cancelBtn?.addEventListener('click', this.handleCancel.bind(this));
        this.elements.confirmBtn?.addEventListener('click', this.handleConfirm.bind(this));
        
        // 快速搜索
        this.elements.searchInput?.addEventListener('input', this.handleSearch.bind(this));
        
        // 键盘快捷键
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // 点击外部关闭提示
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }
    
    /**
     * 绑定字段事件
     */
    bindFieldEvents(elementId, fieldName) {
        const element = this.elements[elementId];
        if (element) {
            element.addEventListener('input', (e) => {
                this.handleFieldChange(fieldName, e.target.value);
            });
            element.addEventListener('change', (e) => {
                this.handleFieldChange(fieldName, e.target.value);
            });
        }
    }
    
    /**
     * 加载订单数据
     */
    async loadOrderData() {
        this.showLoading(true);
        
        try {
            // 从URL参数或应用状态获取订单数据
            const urlParams = utils.parseUrlParams();
            const orderId = urlParams.orderId;
            
            if (orderId) {
                // 从API加载订单数据
                this.currentOrderData = await this.fetchOrderData(orderId);
            } else {
                // 使用当前订单数据或示例数据
                this.currentOrderData = appState.get('currentOrder.formData') || this.getExampleOrderData();
            }
            
            // 保存原始数据用于比较
            this.originalOrderData = utils.deepClone(this.currentOrderData);
            
            // 填充UI
            this.populateOrderData();
            
        } catch (error) {
            logger.logError('加载订单数据失败', error);
            this.showNotification('加载订单数据失败', 'error');
            
            // 使用示例数据
            this.currentOrderData = this.getExampleOrderData();
            this.populateOrderData();
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 获取示例订单数据
     */
    getExampleOrderData() {
        return {
            ota_reference_number: 'GMH-240315001',
            sub_category_id: '2',
            car_type_id: '5',
            incharge_by_backend_user_id: '310',
            customer_name: '张三',
            customer_contact: '+60123456789',
            customer_email: '<EMAIL>',
            flight_info: 'MH123',
            pickup: 'KLIA2国际机场',
            destination: '吉隆坡双子塔',
            date: '2024-03-15',
            time: '14:30',
            passenger_number: 3,
            luggage_number: 2,
            driving_region_id: '1',
            languages_id_array: ['2', '4'],
            ota_price: 120.00,
            driver_fee: 100.00,
            driver_collect: 20.00,
            baby_chair: true,
            tour_guide: false,
            meet_and_greet: false,
            extra_requirement: '需要中文司机',
            status: 'pending',
            created_at: '2024-03-15T14:30:00Z'
        };
    }
    
    /**
     * 填充订单数据到UI
     */
    populateOrderData() {
        try {
            // 更新状态显示
            this.updateStatusDisplay();
            
            // 填充基本信息
            this.updateFieldDisplay('displayOtaRef', 'editOtaRef', this.currentOrderData.ota_reference_number);
            this.updateFieldDisplay('displaySubCategory', 'editSubCategory', this.currentOrderData.sub_category_id, this.getSubCategoryName);
            this.updateFieldDisplay('displayIncharge', 'editIncharge', this.currentOrderData.incharge_by_backend_user_id, this.getBackendUserName);
            
            // 填充行程信息
            this.updateFieldDisplay('displayPickup', 'editPickup', this.currentOrderData.pickup);
            this.updateFieldDisplay('displayDestination', 'editDestination', this.currentOrderData.destination);
            this.updateFieldDisplay('displayDate', 'editDate', this.currentOrderData.date);
            this.updateFieldDisplay('displayTime', 'editTime', this.currentOrderData.time);
            this.updateFieldDisplay('displayCarType', 'editCarType', this.currentOrderData.car_type_id, this.getCarTypeName);
            this.updateFieldDisplay('displayPassenger', 'editPassenger', this.currentOrderData.passenger_number, (val) => `${val}人`);
            this.updateFieldDisplay('displayLuggage', 'editLuggage', this.currentOrderData.luggage_number, (val) => `${val}件`);
            this.updateFieldDisplay('displayRegion', 'editRegion', this.currentOrderData.driving_region_id, this.getRegionName);
            
            // 填充客户信息
            this.updateFieldDisplay('displayCustomerName', 'editCustomerName', this.currentOrderData.customer_name);
            this.updateFieldDisplay('displayCustomerPhone', 'editCustomerPhone', this.currentOrderData.customer_contact);
            this.updateFieldDisplay('displayCustomerEmail', 'editCustomerEmail', this.currentOrderData.customer_email);
            this.updateFieldDisplay('displayFlightInfo', 'editFlightInfo', this.currentOrderData.flight_info);
            
            // 填充语言偏好
            if (this.currentOrderData.languages_id_array) {
                const languageNames = this.currentOrderData.languages_id_array.map(id => this.getLanguageName(id)).join(', ');
                this.updateFieldDisplay('displayLanguages', 'editLanguages', this.currentOrderData.languages_id_array, () => languageNames);
                
                // 设置多选框选中状态
                if (this.elements.editLanguages) {
                    Array.from(this.elements.editLanguages.options).forEach(option => {
                        option.selected = this.currentOrderData.languages_id_array.includes(option.value);
                    });
                }
            }
            
            // 填充费用信息
            this.updateFieldDisplay('displayOtaPrice', 'editOtaPrice', this.currentOrderData.ota_price, (val) => `RM ${parseFloat(val).toFixed(2)}`);
            this.updateFieldDisplay('displayDriverFee', 'editDriverFee', this.currentOrderData.driver_fee, (val) => `RM ${parseFloat(val).toFixed(2)}`);
            this.updateFieldDisplay('displayDriverCollect', 'editDriverCollect', this.currentOrderData.driver_collect, (val) => `RM ${parseFloat(val).toFixed(2)}`);
            
            // 填充特殊要求
            if (this.elements.babyChair) this.elements.babyChair.checked = Boolean(this.currentOrderData.baby_chair);
            if (this.elements.tourGuide) this.elements.tourGuide.checked = Boolean(this.currentOrderData.tour_guide);
            if (this.elements.meetGreet) this.elements.meetGreet.checked = Boolean(this.currentOrderData.meet_and_greet);
            this.updateFieldDisplay('displayExtraReq', 'editExtraReq', this.currentOrderData.extra_requirement);
            
            // 更新费用总计
            this.updateTotalAmount();
            
            // 更新验证状态
            this.updateValidationStatus();
            
            logger.logUserAction('订单数据填充完成', { orderId: this.currentOrderData.ota_reference_number });
            
        } catch (error) {
            logger.logError('填充订单数据失败', error);
            this.showNotification('数据显示异常', 'error');
        }
    }
    
    /**
     * 更新字段显示
     */
    updateFieldDisplay(displayId, editId, value, formatter = null) {
        const displayElement = this.elements[displayId];
        const editElement = this.elements[editId];
        
        if (value !== undefined && value !== null && value !== '') {
            const displayValue = formatter ? formatter(value) : String(value);
            
            if (displayElement) {
                displayElement.textContent = displayValue;
            }
            
            if (editElement) {
                editElement.value = value;
            }
        }
    }
    
    /**
     * 初始化智能匹配
     */
    initializeSmartMatching() {
        // 执行初始智能匹配
        this.performSmartMatching();
        
        // 更新智能提示
        this.updateSmartHints();
        
        logger.log('智能匹配系统已初始化', 'info');
    }
    
    /**
     * 执行智能匹配
     */
    performSmartMatching() {
        const data = this.currentOrderData;
        const confidence = {};
        
        // 智能匹配子分类
        if (data.pickup || data.destination) {
            const text = `${data.pickup || ''} ${data.destination || ''}`.toLowerCase();
            const matchedSubCategory = this.matchSubCategory(text);
            if (matchedSubCategory) {
                confidence.subCategory = matchedSubCategory.confidence;
                if (matchedSubCategory.confidence > 0.7) {
                    data.sub_category_id = matchedSubCategory.id;
                    this.updateFieldDisplay('displaySubCategory', 'editSubCategory', data.sub_category_id, this.getSubCategoryName);
                }
            }
        }
        
        // 智能推荐车型
        if (data.passenger_number) {
            const recommendedCarType = this.recommendCarType(data.passenger_number);
            confidence.carType = 0.9;
            data.car_type_id = recommendedCarType;
            this.updateFieldDisplay('displayCarType', 'editCarType', data.car_type_id, this.getCarTypeName);
        }
        
        // 智能匹配行驶区域
        if (data.pickup || data.destination) {
            const text = `${data.pickup || ''} ${data.destination || ''}`.toLowerCase();
            const matchedRegion = this.matchRegion(text);
            if (matchedRegion) {
                confidence.region = matchedRegion.confidence;
                if (matchedRegion.confidence > 0.6) {
                    data.driving_region_id = matchedRegion.id;
                    this.updateFieldDisplay('displayRegion', 'editRegion', data.driving_region_id, this.getRegionName);
                }
            }
        }
        
        // 智能设置语言偏好
        if (data.customer_name || data.extra_requirement) {
            const text = `${data.customer_name || ''} ${data.extra_requirement || ''}`.toLowerCase();
            const matchedLanguages = this.matchLanguages(text);
            if (matchedLanguages.length > 0) {
                confidence.languages = 0.8;
                data.languages_id_array = matchedLanguages;
                const languageNames = matchedLanguages.map(id => this.getLanguageName(id)).join(', ');
                this.updateFieldDisplay('displayLanguages', 'editLanguages', matchedLanguages, () => languageNames);
                
                // 更新多选框
                if (this.elements.editLanguages) {
                    Array.from(this.elements.editLanguages.options).forEach(option => {
                        option.selected = matchedLanguages.includes(option.value);
                    });
                }
            }
        }
        
        // 保存置信度
        this.smartMatching.confidence = confidence;
        
        // 更新智能指示器
        this.updateSmartIndicators(confidence);
        
        logger.log('智能匹配完成', 'success', { confidence });
    }
    
    /**
     * 匹配子分类
     */
    matchSubCategory(text) {
        const rules = this.matchingRules.subCategory.keywords;
        let bestMatch = null;
        let maxScore = 0;
        
        for (const [keyword, categoryId] of Object.entries(rules)) {
            if (text.includes(keyword.toLowerCase())) {
                const score = keyword.length / text.length;
                if (score > maxScore) {
                    maxScore = score;
                    bestMatch = { id: categoryId, confidence: Math.min(score * 2, 1) };
                }
            }
        }
        
        return bestMatch;
    }
    
    /**
     * 推荐车型
     */
    recommendCarType(passengerCount) {
        const carTypes = [
            { id: '5', limit: 3 },
            { id: '15', limit: 5 },
            { id: '36', limit: 6 },
            { id: '20', limit: 7 },
            { id: '23', limit: 10 }
        ];
        
        for (const carType of carTypes) {
            if (passengerCount <= carType.limit) {
                return carType.id;
            }
        }
        
        return '23'; // 默认最大车型
    }
    
    /**
     * 匹配行驶区域
     */
    matchRegion(text) {
        const rules = this.matchingRules.region.keywords;
        let bestMatch = null;
        let maxScore = 0;
        
        for (const [keyword, regionId] of Object.entries(rules)) {
            if (text.includes(keyword.toLowerCase())) {
                const score = keyword.length / text.length;
                if (score > maxScore) {
                    maxScore = score;
                    bestMatch = { id: regionId, confidence: Math.min(score * 3, 1) };
                }
            }
        }
        
        return bestMatch;
    }
    
    /**
     * 匹配语言偏好
     */
    matchLanguages(text) {
        const rules = this.matchingRules.language.keywords;
        const matchedLanguages = [];
        
        for (const [keyword, languageIds] of Object.entries(rules)) {
            if (text.includes(keyword.toLowerCase())) {
                matchedLanguages.push(...languageIds);
            }
        }
        
        // 去重并确保至少包含英文
        const uniqueLanguages = [...new Set(matchedLanguages)];
        if (uniqueLanguages.length === 0 || !uniqueLanguages.includes('2')) {
            uniqueLanguages.unshift('2'); // 默认包含英文
        }
        
        return uniqueLanguages.map(String);
    }
    
    /**
     * 更新智能指示器
     */
    updateSmartIndicators(confidence) {
        // 基本信息指示器
        const basicScore = (confidence.subCategory || 0);
        this.updateIndicator('basicIndicator', basicScore);
        
        // 行程信息指示器
        const tripScore = Math.max(confidence.carType || 0, confidence.region || 0);
        this.updateIndicator('tripIndicator', tripScore);
        
        // 客户信息指示器
        const customerScore = (confidence.languages || 0);
        this.updateIndicator('customerIndicator', customerScore);
    }
    
    /**
     * 更新单个指示器
     */
    updateIndicator(indicatorId, score) {
        const indicator = this.elements[indicatorId];
        if (indicator) {
            indicator.className = 'indicator';
            if (score >= 0.8) {
                indicator.classList.add('success');
            } else if (score >= 0.5) {
                indicator.classList.add('warning');
            } else {
                indicator.classList.add('error');
            }
        }
    }
    
    /**
     * 更新智能提示
     */
    updateSmartHints() {
        const confidence = this.smartMatching.confidence;
        
        // 子分类提示
        if (this.elements.subCategoryHint) {
            const score = confidence.subCategory || 0;
            this.elements.subCategoryHint.textContent = score > 0.7 ? '已智能匹配' : '建议手动选择';
        }
        
        // 车型提示
        if (this.elements.carTypeHint) {
            const score = confidence.carType || 0;
            this.elements.carTypeHint.textContent = score > 0.8 ? '根据人数推荐' : '请确认车型';
        }
        
        // 区域提示
        if (this.elements.regionHint) {
            const score = confidence.region || 0;
            this.elements.regionHint.textContent = score > 0.6 ? '基于目的地匹配' : '建议手动选择';
        }
        
        // 语言提示
        if (this.elements.languageHint) {
            const score = confidence.languages || 0;
            this.elements.languageHint.textContent = score > 0.7 ? '基于客户信息推荐' : '请确认语言偏好';
        }
    }
    
    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        
        if (this.isEditMode) {
            this.elements.app.classList.add('edit-mode');
            this.elements.editBtn.style.display = 'none';
            this.elements.saveBtn.style.display = 'block';
            
            logger.logUserAction('进入编辑模式');
            this.showNotification('编辑模式已启用', 'info');
        } else {
            this.exitEditMode();
        }
    }
    
    /**
     * 退出编辑模式
     */
    exitEditMode() {
        this.elements.app.classList.remove('edit-mode');
        this.elements.editBtn.style.display = 'block';
        this.elements.saveBtn.style.display = 'none';
        this.isEditMode = false;
        
        if (this.hasUnsavedChanges) {
            if (confirm('有未保存的更改，是否保存？')) {
                this.saveChanges();
            } else {
                this.discardChanges();
            }
        }
        
        logger.logUserAction('退出编辑模式');
    }
    
    /**
     * 处理字段变更
     */
    handleFieldChange(fieldName, newValue) {
        const oldValue = this.currentOrderData[fieldName];
        
        if (oldValue !== newValue) {
            this.currentOrderData[fieldName] = newValue;
            this.hasUnsavedChanges = true;
            
            // 实时更新显示值
            this.updateDisplayValue(fieldName, newValue);
            
            // 触发智能匹配
            this.triggerSmartMatching(fieldName, newValue);
            
            // 更新验证状态
            this.updateValidationStatus();
            
            // 更新费用计算
            if (['ota_price', 'driver_fee', 'driver_collect'].includes(fieldName)) {
                this.updateTotalAmount();
            }
            
            logger.logDataChange(fieldName, oldValue, newValue, 'compact_edit');
        }
    }
    
    /**
     * 更新显示值
     */
    updateDisplayValue(fieldName, value) {
        const fieldMapping = {
            'ota_reference_number': 'displayOtaRef',
            'sub_category_id': 'displaySubCategory',
            'incharge_by_backend_user_id': 'displayIncharge',
            'pickup': 'displayPickup',
            'destination': 'displayDestination',
            'date': 'displayDate',
            'time': 'displayTime',
            'car_type_id': 'displayCarType',
            'passenger_number': 'displayPassenger',
            'luggage_number': 'displayLuggage',
            'driving_region_id': 'displayRegion',
            'customer_name': 'displayCustomerName',
            'customer_contact': 'displayCustomerPhone',
            'customer_email': 'displayCustomerEmail',
            'flight_info': 'displayFlightInfo',
            'ota_price': 'displayOtaPrice',
            'driver_fee': 'displayDriverFee',
            'driver_collect': 'displayDriverCollect',
            'extra_requirement': 'displayExtraReq'
        };
        
        const displayElementId = fieldMapping[fieldName];
        const displayElement = this.elements[displayElementId];
        
        if (displayElement && value !== undefined && value !== null && value !== '') {
            let displayValue = String(value);
            
            // 格式化特殊字段
            switch (fieldName) {
                case 'sub_category_id':
                    displayValue = this.getSubCategoryName(value);
                    break;
                case 'car_type_id':
                    displayValue = this.getCarTypeName(value);
                    break;
                case 'incharge_by_backend_user_id':
                    displayValue = this.getBackendUserName(value);
                    break;
                case 'driving_region_id':
                    displayValue = this.getRegionName(value);
                    break;
                case 'passenger_number':
                    displayValue = `${value}人`;
                    break;
                case 'luggage_number':
                    displayValue = `${value}件`;
                    break;
                case 'ota_price':
                case 'driver_fee':
                case 'driver_collect':
                    displayValue = `RM ${parseFloat(value).toFixed(2)}`;
                    break;
            }
            
            displayElement.textContent = displayValue;
        }
    }
    
    /**
     * 触发智能匹配
     */
    triggerSmartMatching(fieldName, value) {
        // 根据字段变更触发相应的智能匹配
        switch (fieldName) {
            case 'pickup':
            case 'destination':
                this.performLocationBasedMatching();
                break;
            case 'passenger_number':
                this.performCarTypeMatching(parseInt(value));
                break;
            case 'customer_name':
            case 'extra_requirement':
                this.performLanguageMatching();
                break;
        }
    }
    
    /**
     * 基于地点的智能匹配
     */
    performLocationBasedMatching() {
        const pickup = this.currentOrderData.pickup || '';
        const destination = this.currentOrderData.destination || '';
        const text = `${pickup} ${destination}`.toLowerCase();
        
        // 匹配子分类
        const subCategoryMatch = this.matchSubCategory(text);
        if (subCategoryMatch && subCategoryMatch.confidence > 0.7) {
            this.currentOrderData.sub_category_id = subCategoryMatch.id;
            this.updateFieldDisplay('displaySubCategory', 'editSubCategory', subCategoryMatch.id, this.getSubCategoryName);
            this.elements.subCategoryHint.textContent = '已智能匹配';
        }
        
        // 匹配行驶区域
        const regionMatch = this.matchRegion(text);
        if (regionMatch && regionMatch.confidence > 0.6) {
            this.currentOrderData.driving_region_id = regionMatch.id;
            this.updateFieldDisplay('displayRegion', 'editRegion', regionMatch.id, this.getRegionName);
            this.elements.regionHint.textContent = '基于目的地匹配';
        }
    }
    
    /**
     * 车型智能匹配
     */
    performCarTypeMatching(passengerCount) {
        if (!isNaN(passengerCount) && passengerCount > 0) {
            const recommendedCarType = this.recommendCarType(passengerCount);
            this.currentOrderData.car_type_id = recommendedCarType;
            this.updateFieldDisplay('displayCarType', 'editCarType', recommendedCarType, this.getCarTypeName);
            this.elements.carTypeHint.textContent = '根据人数推荐';
            
            // 自动设置行李数量建议
            const suggestedLuggage = Math.min(passengerCount, 4);
            this.currentOrderData.luggage_number = suggestedLuggage;
            this.updateFieldDisplay('displayLuggage', 'editLuggage', suggestedLuggage, (val) => `${val}件`);
            
            this.showNotification(`已推荐适合${passengerCount}人的车型`, 'info');
        }
    }
    
    /**
     * 语言智能匹配
     */
    performLanguageMatching() {
        const customerName = this.currentOrderData.customer_name || '';
        const extraReq = this.currentOrderData.extra_requirement || '';
        const text = `${customerName} ${extraReq}`.toLowerCase();
        
        const matchedLanguages = this.matchLanguages(text);
        if (matchedLanguages.length > 0) {
            this.currentOrderData.languages_id_array = matchedLanguages;
            const languageNames = matchedLanguages.map(id => this.getLanguageName(id)).join(', ');
            this.updateFieldDisplay('displayLanguages', 'editLanguages', matchedLanguages, () => languageNames);
            
            // 更新多选框
            if (this.elements.editLanguages) {
                Array.from(this.elements.editLanguages.options).forEach(option => {
                    option.selected = matchedLanguages.includes(option.value);
                });
            }
            
            this.elements.languageHint.textContent = '基于客户信息推荐';
        }
    }
    
    /**
     * 处理乘客人数变化
     */
    handlePassengerChange(event) {
        const passengerCount = parseInt(event.target.value);
        this.handleFieldChange('passenger_number', passengerCount);
    }
    
    /**
     * 处理地点变化
     */
    handleLocationChange(event) {
        const fieldName = event.target.id === 'editPickup' ? 'pickup' : 'destination';
        this.handleFieldChange(fieldName, event.target.value);
    }
    
    /**
     * 更新费用总计
     */
    updateTotalAmount() {
        const otaPrice = parseFloat(this.currentOrderData.ota_price || 0);
        if (this.elements.totalAmount) {
            this.elements.totalAmount.textContent = `RM ${otaPrice.toFixed(2)}`;
        }
    }
    
    /**
     * 更新验证状态
     */
    updateValidationStatus() {
        const validation = this.validateOrderData();
        const statusIcon = this.elements.validationStatus?.querySelector('.status-icon');
        const statusText = this.elements.validationStatus?.querySelector('.status-text');
        
        if (statusIcon && statusText) {
            if (validation.isValid) {
                statusIcon.textContent = '✓';
                statusIcon.style.color = 'var(--compact-success)';
                statusText.textContent = '数据完整';
            } else {
                statusIcon.textContent = '⚠';
                statusIcon.style.color = 'var(--compact-warning)';
                statusText.textContent = `${Object.keys(validation.errors).length}项需要修正`;
            }
        }
    }
    
    /**
     * 验证订单数据
     */
    validateOrderData() {
        const errors = {};
        const data = this.currentOrderData;
        
        // 必填字段验证
        const requiredFields = [
            'ota_reference_number',
            'sub_category_id',
            'car_type_id',
            'incharge_by_backend_user_id'
        ];
        
        requiredFields.forEach(field => {
            if (!data[field] || String(data[field]).trim() === '') {
                errors[field] = '此字段为必填项';
            }
        });
        
        // 邮箱格式验证
        if (data.customer_email && !utils.isValidEmail(data.customer_email)) {
            errors.customer_email = '邮箱格式不正确';
        }
        
        // 电话格式验证
        if (data.customer_contact && !utils.isValidPhone(data.customer_contact)) {
            errors.customer_contact = '电话格式不正确';
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
    
    /**
     * 保存更改
     */
    async saveChanges() {
        try {
            this.showLoading(true);
            
            // 验证数据
            const validation = this.validateOrderData();
            if (!validation.isValid) {
                this.showValidationErrors(validation.errors);
                return;
            }
            
            // 保存到服务器
            await this.saveOrderData(this.currentOrderData);
            
            // 更新原始数据
            this.originalOrderData = utils.deepClone(this.currentOrderData);
            this.hasUnsavedChanges = false;
            
            // 退出编辑模式
            this.exitEditMode();
            
            logger.logUserAction('订单数据保存成功', { orderId: this.currentOrderData.ota_reference_number });
            this.showNotification('保存成功', 'success');
            
        } catch (error) {
            logger.logError('保存订单数据失败', error);
            this.showNotification('保存失败，请重试', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 显示验证错误
     */
    showValidationErrors(errors) {
        const errorMessages = Object.entries(errors).map(([field, message]) => {
            return `${this.getFieldDisplayName(field)}: ${message}`;
        });
        
        this.showNotification(`验证失败:\n${errorMessages.join('\n')}`, 'error');
    }
    
    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(fieldName) {
        const fieldNames = {
            'ota_reference_number': '订单号',
            'sub_category_id': '服务类型',
            'car_type_id': '车型',
            'incharge_by_backend_user_id': '负责人',
            'customer_email': '客户邮箱',
            'customer_contact': '联系电话'
        };
        return fieldNames[fieldName] || fieldName;
    }
    
    /**
     * 名称映射方法
     */
    getSubCategoryName(id) {
        const categories = { '2': '接机服务', '3': '送机服务', '4': '包车服务', '25': '包车（商铺）' };
        return categories[id] || `子分类 ${id}`;
    }
    
    getCarTypeName(id) {
        const carTypes = {
            '5': '5座舒适型',
            '15': '7座MPV',
            '20': '10座商务车',
            '23': '14座大型车',
            '36': 'Alphard豪华型'
        };
        return carTypes[id] || `车型 ${id}`;
    }
    
    getBackendUserName(id) {
        const users = { '310': 'Jcy', '311': 'opAnnie', '37': 'smw', '420': 'chongyoonlim' };
        return users[id] || `用户 ${id}`;
    }
    
    getRegionName(id) {
        const regions = { '1': '吉隆坡/雪兰莪', '2': '槟城', '3': '柔佛', '4': '沙巴' };
        return regions[id] || `区域 ${id}`;
    }
    
    getLanguageName(id) {
        const languages = { '2': '英文', '3': '马来文', '4': '中文' };
        return languages[id] || `语言 ${id}`;
    }
    
    /**
     * 事件处理方法
     */
    handleBack() {
        if (this.hasUnsavedChanges) {
            if (confirm('有未保存的更改，确定要离开吗？')) {
                this.navigateBack();
            }
        } else {
            this.navigateBack();
        }
    }
    
    navigateBack() {
        window.history.back();
    }
    
    handleReset() {
        if (confirm('确定要重置所有更改吗？')) {
            this.currentOrderData = utils.deepClone(this.originalOrderData);
            this.hasUnsavedChanges = false;
            this.populateOrderData();
            this.showNotification('已重置到原始状态', 'info');
        }
    }
    
    handlePreview() {
        this.showOrderPreview();
    }
    
    handleValidate() {
        const validation = this.validateOrderData();
        if (validation.isValid) {
            this.showNotification('数据验证通过！', 'success');
        } else {
            this.showValidationErrors(validation.errors);
        }
    }
    
    handleCancel() {
        if (confirm('确定要取消此订单吗？')) {
            this.showNotification('订单已取消', 'info');
        }
    }
    
    handleConfirm() {
        if (confirm('确定要确认此订单吗？')) {
            this.confirmOrder();
        }
    }
    
    handleKeyboard(event) {
        // ESC键退出编辑模式
        if (event.key === 'Escape' && this.isEditMode) {
            this.toggleEditMode();
        }
        
        // Ctrl+S保存
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            if (this.isEditMode) {
                this.saveChanges();
            }
        }
        
        // Ctrl+E编辑
        if (event.ctrlKey && event.key === 'e') {
            event.preventDefault();
            this.toggleEditMode();
        }
    }
    
    handleOutsideClick(event) {
        // 点击外部关闭智能提示
        if (this.elements.smartTooltip.style.display === 'block' && 
            !this.elements.smartTooltip.contains(event.target)) {
            this.hideSmartTooltip();
        }
        
        // 点击外部关闭快速搜索
        if (this.elements.quickSearch.style.display === 'block' && 
            !this.elements.quickSearch.contains(event.target)) {
            this.hideQuickSearch();
        }
    }
    
    /**
     * UI辅助方法
     */
    showLoading(show) {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }
    
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--compact-bg-primary);
            border: 1px solid var(--compact-border-medium);
            border-radius: var(--compact-radius-lg);
            padding: var(--compact-space-xl);
            box-shadow: var(--compact-shadow-lg);
            z-index: 1000;
            max-width: 300px;
            font-size: var(--compact-text-sm);
            color: var(--compact-text-primary);
            transform: translateX(100%);
            transition: transform var(--compact-transition);
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                notification.style.borderLeftColor = 'var(--compact-success)';
                break;
            case 'error':
                notification.style.borderLeftColor = 'var(--compact-error)';
                break;
            case 'warning':
                notification.style.borderLeftColor = 'var(--compact-warning)';
                break;
        }
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * 初始化UI状态
     */
    initializeUI() {
        // 设置初始状态
        this.updateValidationStatus();
        this.updateTotalAmount();
        
        // 隐藏保存按钮
        if (this.elements.saveBtn) {
            this.elements.saveBtn.style.display = 'none';
        }
    }
    
    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        if (this.elements.orderId) {
            this.elements.orderId.textContent = this.currentOrderData.ota_reference_number || 'GMH-240315001';
        }
        
        if (this.elements.orderStatus) {
            const statusMap = {
                'pending': '待确认',
                'confirmed': '已确认',
                'in_progress': '进行中',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            this.elements.orderStatus.textContent = statusMap[this.currentOrderData.status] || '待确认';
        }
    }
    
    /**
     * API相关方法（模拟实现）
     */
    async fetchOrderData(orderId) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.getExampleOrderData());
            }, 1000);
        });
    }
    
    async saveOrderData(data) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({ success: true });
            }, 1500);
        });
    }
    
    async confirmOrder() {
        try {
            this.showLoading(true);
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000));
            this.currentOrderData.status = 'confirmed';
            this.updateStatusDisplay();
            this.showNotification('订单已确认', 'success');
        } catch (error) {
            this.showNotification('确认失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 放弃更改
     */
    discardChanges() {
        this.currentOrderData = utils.deepClone(this.originalOrderData);
        this.hasUnsavedChanges = false;
        this.populateOrderData();
    }
    
    /**
     * 显示订单预览
     */
    showOrderPreview() {
        const previewData = this.generatePreviewText();
        alert(`订单预览:\n\n${previewData}`);
    }
    
    /**
     * 生成预览文本
     */
    generatePreviewText() {
        const data = this.currentOrderData;
        return `
订单号: ${data.ota_reference_number}
服务类型: ${this.getSubCategoryName(data.sub_category_id)}
客户: ${data.customer_name}
电话: ${data.customer_contact}
路线: ${data.pickup} → ${data.destination}
时间: ${data.date} ${data.time}
车型: ${this.getCarTypeName(data.car_type_id)}
人数: ${data.passenger_number}人
价格: RM ${data.ota_price}
        `.trim();
    }
}

// 初始化紧凑型订单预览
const compactOrderPreview = new CompactOrderPreview();

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => compactOrderPreview.init());
} else {
    compactOrderPreview.init();
}

// 导出实例
export default compactOrderPreview;