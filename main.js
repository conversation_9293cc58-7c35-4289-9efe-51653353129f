/**
 * OTA订单处理系统 - 主入口文件
 * 负责系统初始化和启动
 */

import appState from './js/app-state.js';
import apiService from './js/api-service.js';
import geminiService from './js/gemini-service.js';
import uiManager from './js/ui-manager.js';
import logger from './js/logger.js';
import utils from './js/utils.js';

/**
 * 应用程序类
 */
class OTAApplication {
    constructor() {
        this.isInitialized = false;
        this.startTime = Date.now();
        this.version = '1.0.0';
        
        // 绑定方法上下文
        this.handleUnload = this.handleUnload.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
    }
    
    /**
     * 初始化应用程序
     */
    async init() {
        try {
            logger.log('开始初始化OTA订单处理系统', 'info', {
                version: this.version,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            
            // 设置性能监控
            utils.performanceMonitor.mark('app-init');
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 设置错误处理
            this.setupErrorHandling();
            
            // 设置环境变量（如果有）
            this.loadEnvironmentVariables();
            
            // 初始化各个模块
            await this.initializeModules();
            
            // 设置系统事件监听
            this.setupEventListeners();
            
            // 标记初始化完成
            this.isInitialized = true;
            
            const initTime = utils.performanceMonitor.measure('app-init');
            logger.log('OTA订单处理系统初始化完成', 'success', {
                initTime: `${initTime.toFixed(2)}ms`,
                modules: ['appState', 'apiService', 'geminiService', 'uiManager', 'logger']
            });
            
            // 显示系统信息
            this.displaySystemInfo();
            
        } catch (error) {
            logger.logError('系统初始化失败', error);
            this.showInitializationError(error);
        }
    }
    
    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const browserInfo = utils.getBrowserInfo();
        const requiredFeatures = [
            'fetch',
            'localStorage',
            'Promise',
            'URLSearchParams'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            switch (feature) {
                case 'fetch':
                    return !window.fetch;
                case 'localStorage':
                    return !window.localStorage;
                case 'Promise':
                    return !window.Promise;
                case 'URLSearchParams':
                    return !window.URLSearchParams;
                default:
                    return false;
            }
        });
        
        if (missingFeatures.length > 0) {
            const message = `浏览器不支持以下功能: ${missingFeatures.join(', ')}`;
            logger.logError('浏览器兼容性检查失败', { 
                browserInfo, 
                missingFeatures 
            });
            throw new Error(message);
        }
        
        logger.log('浏览器兼容性检查通过', 'success', browserInfo);
    }
    
    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', this.handleError);
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        
        // 页面卸载处理
        window.addEventListener('beforeunload', this.handleUnload);
        
        logger.log('错误处理机制已设置', 'info');
    }
    
    /**
     * 加载环境变量
     */
    loadEnvironmentVariables() {
        // 尝试从meta标签获取配置
        const configMeta = document.querySelector('meta[name="ota-config"]');
        if (configMeta) {
            try {
                const config = JSON.parse(configMeta.content);
                Object.entries(config).forEach(([key, value]) => {
                    appState.set(`config.${key}`, value, false);
                });
                logger.log('环境配置已加载', 'info', config);
            } catch (error) {
                logger.logError('环境配置解析失败', error);
            }
        }
        
        // 检查URL参数中的配置
        const urlParams = utils.parseUrlParams();
        if (urlParams.debug === 'true') {
            appState.setDebugMode(true);
            logger.log('调试模式已启用（URL参数）', 'info');
        }
        
        if (urlParams.theme) {
            appState.setTheme(urlParams.theme);
            logger.log(`主题已设置为: ${urlParams.theme}`, 'info');
        }
    }
    
    /**
     * 初始化各个模块
     */
    async initializeModules() {
        logger.log('开始初始化模块', 'info');
        
        // 初始化UI管理器
        utils.performanceMonitor.mark('ui-init');
        uiManager.init();
        const uiInitTime = utils.performanceMonitor.measure('ui-init');
        logger.log('UI管理器初始化完成', 'success', { 
            time: `${uiInitTime.toFixed(2)}ms` 
        });
        
        // 如果用户已登录，尝试获取系统数据
        if (appState.get('auth.isLoggedIn')) {
            try {
                utils.performanceMonitor.mark('system-data-load');
                
                // 检查缓存的系统数据是否过期
                const lastUpdated = appState.get('systemData.lastUpdated');
                const needsRefresh = !lastUpdated || 
                    (Date.now() - new Date(lastUpdated).getTime()) > 24 * 60 * 60 * 1000;
                
                if (needsRefresh) {
                    logger.log('系统数据已过期，重新获取', 'info');
                    await apiService.getAllSystemData();
                } else {
                    logger.log('使用缓存的系统数据', 'info');
                }
                
                const dataLoadTime = utils.performanceMonitor.measure('system-data-load');
                logger.log('系统数据加载完成', 'success', { 
                    time: `${dataLoadTime.toFixed(2)}ms`,
                    fromCache: !needsRefresh
                });
                
            } catch (error) {
                logger.logError('系统数据加载失败', error);
                // 不阻断初始化，使用静态数据
            }
        }
        
        // 检查Gemini API可用性（已内嵌配置）
        if (geminiService.isAvailable()) {
            logger.log('Gemini AI服务可用', 'success', {
                model: geminiService.modelVersion,
                apiKeyConfigured: true
            });
        } else {
            logger.log('Gemini AI服务配置异常', 'error');
        }
    }
    
    /**
     * 设置系统事件监听
     */
    setupEventListeners() {
        // 网络状态监听
        window.addEventListener('online', () => {
            logger.log('网络连接已恢复', 'success');
            appState.set('system.connected', true);
        });
        
        window.addEventListener('offline', () => {
            logger.log('网络连接已断开', 'warning');
            appState.set('system.connected', false);
        });
        
        // 可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                logger.logUserAction('页面重新激活');
                // 检查是否需要刷新数据
                this.checkDataFreshness();
            } else {
                logger.logUserAction('页面进入后台');
            }
        });
        
        // 窗口大小变化监听
        const resizeHandler = utils.debounce(() => {
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                isMobile: utils.isMobile()
            };
            logger.logUserAction('窗口大小变化', viewport);
        }, 500);
        
        window.addEventListener('resize', resizeHandler);
        
        logger.log('系统事件监听已设置', 'info');
    }
    
    /**
     * 检查数据新鲜度
     */
    async checkDataFreshness() {
        if (!appState.get('auth.isLoggedIn')) return;
        
        const lastUpdated = appState.get('systemData.lastUpdated');
        if (!lastUpdated) return;
        
        const hoursAgo = (Date.now() - new Date(lastUpdated).getTime()) / (1000 * 60 * 60);
        
        if (hoursAgo > 4) { // 4小时后检查更新
            try {
                logger.log('检查系统数据更新', 'info');
                await apiService.getAllSystemData();
                logger.log('系统数据已更新', 'success');
            } catch (error) {
                logger.logError('系统数据更新失败', error);
            }
        }
    }
    
    /**
     * 处理全局错误
     * @param {ErrorEvent} event - 错误事件
     */
    handleError(event) {
        const error = {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack
        };
        
        logger.logError('全局JavaScript错误', error);
        
        // 如果是关键错误，显示用户友好的错误信息
        if (this.isCriticalError(event.error)) {
            this.showCriticalError(event.error);
        }
    }
    
    /**
     * 处理未捕获的Promise拒绝
     * @param {PromiseRejectionEvent} event - Promise拒绝事件
     */
    handleUnhandledRejection(event) {
        const error = {
            reason: event.reason,
            promise: event.promise
        };
        
        logger.logError('未捕获的Promise拒绝', error);
        
        // 阻止默认的错误显示
        event.preventDefault();
    }
    
    /**
     * 处理页面卸载
     * @param {BeforeUnloadEvent} event - 卸载事件
     */
    handleUnload(event) {
        // 检查是否有未保存的数据
        const hasUnsavedData = this.checkUnsavedData();
        
        if (hasUnsavedData) {
            const message = '您有未保存的数据，确定要离开吗？';
            event.returnValue = message;
            return message;
        }
        
        // 记录会话结束
        const sessionDuration = Date.now() - this.startTime;
        logger.logUserAction('会话结束', {
            duration: `${(sessionDuration / 1000).toFixed(1)}秒`,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 检查是否有未保存的数据
     * @returns {boolean} 是否有未保存的数据
     */
    checkUnsavedData() {
        const currentOrder = appState.get('currentOrder');
        
        // 检查是否有正在编辑的订单
        if (currentOrder && currentOrder.status === 'editing') {
            return true;
        }
        
        // 检查表单是否有内容
        const orderInput = document.getElementById('orderInput');
        if (orderInput && orderInput.value.trim()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否为关键错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否为关键错误
     */
    isCriticalError(error) {
        if (!error) return false;
        
        const criticalErrorPatterns = [
            /memory/i,
            /network/i,
            /security/i,
            /permission/i
        ];
        
        return criticalErrorPatterns.some(pattern => 
            pattern.test(error.message) || pattern.test(error.name)
        );
    }
    
    /**
     * 显示初始化错误
     * @param {Error} error - 错误对象
     */
    showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'init-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>🚨 系统初始化失败</h2>
                <p><strong>错误信息:</strong> ${error.message}</p>
                <p>请尝试以下解决方案：</p>
                <ul>
                    <li>刷新页面重试</li>
                    <li>清除浏览器缓存</li>
                    <li>检查网络连接</li>
                    <li>更新浏览器版本</li>
                </ul>
                <button onclick="location.reload()" class="btn btn-primary">
                    重新加载
                </button>
            </div>
        `;
        
        errorContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        `;
        
        document.body.appendChild(errorContainer);
    }
    
    /**
     * 显示关键错误
     * @param {Error} error - 错误对象
     */
    showCriticalError(error) {
        // 创建错误提示
        const errorAlert = document.createElement('div');
        errorAlert.className = 'critical-error-alert';
        errorAlert.innerHTML = `
            <div class="alert alert-error">
                <span class="alert-icon">⚠️</span>
                <span class="alert-message">发生严重错误，请刷新页面重试</span>
                <button class="alert-action" onclick="location.reload()">刷新</button>
            </div>
        `;
        
        document.body.appendChild(errorAlert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (errorAlert.parentNode) {
                errorAlert.parentNode.removeChild(errorAlert);
            }
        }, 3000);
    }
    
    /**
     * 显示系统信息
     */
    displaySystemInfo() {
        const systemInfo = {
            version: this.version,
            initTime: `${Date.now() - this.startTime}ms`,
            browser: utils.getBrowserInfo(),
            features: {
                geminiAI: geminiService.isAvailable(),
                localStorage: !!window.localStorage,
                clipboard: !!navigator.clipboard
            },
            performance: utils.performanceMonitor.getStats()
        };
        
        logger.log('系统信息', 'info', systemInfo);
        
        // 在调试模式下显示到控制台
        if (appState.get('config.debugMode')) {
            console.group('🚗 OTA订单处理系统');
            console.log('版本:', this.version);
            console.log('初始化时间:', systemInfo.initTime);
            console.log('浏览器:', systemInfo.browser);
            console.log('功能支持:', systemInfo.features);
            console.groupEnd();
        }
    }
    
    /**
     * 获取应用状态
     * @returns {object} 应用状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            version: this.version,
            startTime: this.startTime,
            uptime: Date.now() - this.startTime,
            modules: {
                appState: !!window.appState,
                apiService: !!window.apiService,
                geminiService: !!window.geminiService,
                uiManager: !!window.uiManager,
                logger: !!window.logger
            }
        };
    }
    
    /**
     * 重启应用
     */
    async restart() {
        logger.log('重启应用程序', 'info');
        
        try {
            // 清理现有状态
            this.cleanup();
            
            // 重新初始化
            await this.init();
            
            logger.log('应用程序重启完成', 'success');
        } catch (error) {
            logger.logError('应用程序重启失败', error);
            throw error;
        }
    }
    
    /**
     * 清理应用资源
     */
    cleanup() {
        // 移除事件监听器
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
        window.removeEventListener('beforeunload', this.handleUnload);
        
        // 清理性能监控
        utils.performanceMonitor.clear();
        
        this.isInitialized = false;
        
        logger.log('应用程序清理完成', 'info');
    }
}

// 创建全局应用实例
const app = new OTAApplication();

// 暴露到全局作用域（用于调试）
window.app = app;
window.appState = appState;
window.apiService = apiService;
window.geminiService = geminiService;
window.uiManager = uiManager;
window.logger = logger;
window.utils = utils;

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    // DOM已经加载完成
    app.init();
}

// 导出应用实例
export default app;