# 代码结构

## 目录结构
```
create job/
├── js/                     # JavaScript模块目录
│   ├── app-state.js       # 应用状态管理
│   ├── api-service.js     # API服务模块
│   ├── gemini-service.js  # Gemini AI服务
│   ├── ui-manager.js      # UI管理模块
│   ├── logger.js          # 日志记录模块
│   └── utils.js           # 工具函数集合
├── public/                # 静态资源
├── index.html            # 主页面
├── main.js               # 应用入口文件
├── style.css             # 主样式文件
├── mobile-order-preview.html  # 移动端订单预览
├── mobile-order-preview.js    # 移动端脚本
├── mobile-order-styles.css    # 移动端样式
└── package.json          # 项目配置
```

## 核心模块说明

### AppState (app-state.js)
- 集中式状态管理
- 本地存储持久化
- 状态变更监听机制

### ApiService (api-service.js)
- GoMyHire API集成
- 认证管理
- 数据缓存和降级机制

### GeminiService (gemini-service.js)
- Gemini AI API集成
- 实时订单解析
- 智能内容分析

### UIManager (ui-manager.js)
- DOM操作管理
- 事件处理
- 界面状态同步

### Logger (logger.js)
- 统一日志记录
- 多级别日志支持
- 用户行为追踪

### Utils (utils.js)
- 通用工具函数
- 性能监控
- 数据处理辅助函数

## 模块依赖关系
- main.js → 所有模块（应用入口）
- ui-manager.js → app-state.js, logger.js
- api-service.js → app-state.js, logger.js
- gemini-service.js → app-state.js, logger.js