/**
 * API服务模块
 * 负责与GoMyHire API的所有交互，包括认证、数据获取、订单创建等
 */

import appState from './app-state.js';
import logger from './logger.js';

class ApiService {
    constructor() {
        this.baseURL = 'https://gomyhire.com.my/api';
        this.timeout = 30000; // 30秒超时
        
        // 更新的静态映射数据（基于最新的API文档）
        this.staticData = {
            backendUsers: [
                { id: 1, name: 'Super Admin', email: '', phone: '012345689', role_id: 'super admin' },
                { id: 37, name: 'smw', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 310, name: 'J<PERSON>', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 420, name: 'ch<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 1223, name: 'Chong admin', email: '', phone: '', role_id: 'admin' }
            ],
            carTypes: [
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passenger_limit: 3 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passenger_limit: 5 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passenger_limit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passenger_limit: 10 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passenger_limit: 6 }
            ],
            // 限制的子分类（仅支持三种服务类型）
            subCategories: [
                { id: 2, name: 'Pickup', description: '接机服务' },
                { id: 3, name: 'Dropoff', description: '送机服务' },
                { id: 4, name: 'Charter', description: '包车服务' }
            ],
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' }
            ],
            languages: [
                { id: 2, name: 'English (EN)' },
                { id: 3, name: 'Malay (MY)' },
                { id: 4, name: 'Chinese (CN)' }
            ]
        };
    }
    
    /**
     * 发送HTTP请求的通用方法
     * @param {string} url - 请求URL
     * @param {object} options - 请求选项
     * @returns {Promise<object>} 响应数据
     */
    async request(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        const startTime = Date.now();
        
        try {
            const config = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                signal: controller.signal,
                ...options
            };
            
            // 添加认证头
            const token = appState.get('auth.token');
            if (token && !options.skipAuth) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            
            logger.log('API请求开始', 'info', { 
                url, 
                method: config.method, 
                hasAuth: !!config.headers['Authorization']
            });
            
            const response = await fetch(url, config);
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            logger.logApiCall(url, config.method, options.body, data, duration);
            appState.recordApiCall({ url, method: config.method, status: response.status });
            
            return data;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            
            logger.logApiCall(url, options.method || 'GET', options.body, null, duration);
            logger.logError('API请求失败', { url, error: error.message });
            appState.addError({ type: 'api', url, message: error.message });
            
            throw error;
        }
    }
    
    /**
     * 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password) {
        try {
            logger.logUserAction('尝试登录', { email });
            
            const data = await this.request(`${this.baseURL}/login`, {
                method: 'POST',
                body: JSON.stringify({ email, password }),
                skipAuth: true
            });
            
            if (data.status && data.token) {
                // 提取真实token（去掉数字前缀）
                const tokenParts = data.token.split('|');
                const actualToken = tokenParts.length > 1 ? tokenParts[1] : data.token;
                
                appState.setAuth(actualToken, { email });
                logger.logUserAction('登录成功', { email, tokenLength: actualToken.length });
                
                return { success: true, token: actualToken };
            } else {
                throw new Error(data.message || '登录失败');
            }
        } catch (error) {
            logger.logUserAction('登录失败', { email, error: error.message });
            throw error;
        }
    }
    
    /**
     * 获取后台用户列表
     * @param {string} search - 搜索关键词
     * @returns {Promise<Array>} 用户列表
     */
    async getBackendUsers(search = '') {
        try {
            const data = await this.request(`${this.baseURL}/backend_users?search=${encodeURIComponent(search)}`);
            
            if (data.data && Array.isArray(data.data)) {
                logger.log('获取后台用户成功', 'success', { count: data.data.length });
                return data.data;
            } else {
                throw new Error('无效的响应格式');
            }
        } catch (error) {
            logger.log('获取后台用户失败，使用静态数据', 'warning', { error: error.message });
            return this.staticData.backendUsers;
        }
    }
    
    /**
     * 获取子分类列表
     * @param {string} search - 搜索关键词
     * @returns {Promise<Array>} 子分类列表
     */
    async getSubCategories(search = '') {
        try {
            const data = await this.request(`${this.baseURL}/sub_category?search=${encodeURIComponent(search)}`);
            
            if (data.data && Array.isArray(data.data)) {
                logger.log('获取子分类成功', 'success', { count: data.data.length });
                return data.data;
            } else {
                throw new Error('无效的响应格式');
            }
        } catch (error) {
            logger.log('获取子分类失败，使用静态数据', 'warning', { error: error.message });
            return this.staticData.subCategories;
        }
    }
    
    /**
     * 获取车型列表
     * @param {string} search - 搜索关键词
     * @returns {Promise<Array>} 车型列表
     */
    async getCarTypes(search = '') {
        try {
            const data = await this.request(`${this.baseURL}/car_types?search=${encodeURIComponent(search)}`);
            
            if (data.data && Array.isArray(data.data)) {
                logger.log('获取车型成功', 'success', { count: data.data.length });
                return data.data;
            } else {
                throw new Error('无效的响应格式');
            }
        } catch (error) {
            logger.log('获取车型失败，使用静态数据', 'warning', { error: error.message });
            return this.staticData.carTypes;
        }
    }
    
    /**
     * 获取行驶区域列表
     * @param {string} search - 搜索关键词
     * @returns {Promise<Array>} 区域列表
     */
    async getDrivingRegions(search = '') {
        try {
            const data = await this.request(`${this.baseURL}/driving_regions?search=${encodeURIComponent(search)}`);
            
            if (data.data && Array.isArray(data.data)) {
                logger.log('获取行驶区域成功', 'success', { count: data.data.length });
                return data.data;
            } else {
                throw new Error('无效的响应格式');
            }
        } catch (error) {
            logger.log('获取行驶区域失败，使用静态数据', 'warning', { error: error.message });
            return this.staticData.drivingRegions;
        }
    }
    
    /**
     * 获取语言列表
     * @param {string} search - 搜索关键词
     * @returns {Promise<Array>} 语言列表
     */
    async getLanguages(search = '') {
        try {
            const data = await this.request(`${this.baseURL}/languages?search=${encodeURIComponent(search)}`);
            
            if (data.data && Array.isArray(data.data)) {
                logger.log('获取语言列表成功', 'success', { count: data.data.length });
                return data.data;
            } else {
                throw new Error('无效的响应格式');
            }
        } catch (error) {
            logger.log('获取语言列表失败，使用静态数据', 'warning', { error: error.message });
            return this.staticData.languages;
        }
    }
    
    /**
     * 获取所有系统数据
     * @returns {Promise<object>} 系统数据
     */
    async getAllSystemData() {
        try {
            logger.log('开始获取系统数据', 'info');
            
            const [backendUsers, subCategories, carTypes, drivingRegions, languages] = await Promise.all([
                this.getBackendUsers(),
                this.getSubCategories(),
                this.getCarTypes(),
                this.getDrivingRegions(),
                this.getLanguages()
            ]);
            
            const systemData = {
                backendUsers,
                subCategories,
                carTypes,
                drivingRegions,
                languages
            };
            
            appState.setSystemData(systemData);
            logger.log('系统数据获取完成', 'success', {
                backendUsers: backendUsers.length,
                subCategories: subCategories.length,
                carTypes: carTypes.length,
                drivingRegions: drivingRegions.length,
                languages: languages.length
            });
            
            return systemData;
            
        } catch (error) {
            logger.logError('获取系统数据失败', error);
            throw error;
        }
    }
    
    /**
     * 创建订单
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 创建结果
     */
    async createOrder(orderData) {
        try {
            logger.logUserAction('开始创建订单', { orderData });
            
            // 预处理订单数据
            const processedData = this.preprocessOrderData(orderData);
            
            const data = await this.request(`${this.baseURL}/create_order`, {
                method: 'POST',
                body: JSON.stringify(processedData),
                skipAuth: true // 创建订单API不需要认证
            });
            
            if (data.status === false && data.data?.validation_error) {
                // 验证错误
                logger.log('订单验证失败', 'warning', { errors: data.data.validation_error });
                return { success: false, errors: data.data.validation_error, message: data.message };
            } else if (data.status === true) {
                // 创建成功
                logger.logUserAction('订单创建成功', { orderId: data.data?.id });
                return { success: true, data: data.data };
            } else {
                // 其他错误
                throw new Error(data.message || '订单创建失败');
            }
            
        } catch (error) {
            logger.logError('订单创建异常', { error: error.message, orderData });
            throw error;
        }
    }
    
    /**
     * 预处理订单数据
     * @param {object} orderData - 原始订单数据
     * @returns {object} 处理后的订单数据
     */
    preprocessOrderData(orderData) {
        const processed = { ...orderData };
        
        // 处理日期格式 (YYYY-MM-DD -> DD-MM-YYYY)
        if (processed.date) {
            const dateParts = processed.date.split('-');
            if (dateParts.length === 3) {
                processed.date = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
            }
        }
        
        // 处理语言数组格式 - 使用对象格式避免HTTP 500错误
        if (processed.languages_id_array && Array.isArray(processed.languages_id_array)) {
            // 转换为对象格式 {"0":"1","1":"2","2":"3"}
            const languageObject = {};
            processed.languages_id_array.forEach((id, index) => {
                languageObject[index.toString()] = id.toString();
            });
            processed.languages_id_array = languageObject;
        }
        
        // 确保必需字段为字符串
        ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id'].forEach(field => {
            if (processed[field] !== undefined) {
                processed[field] = String(processed[field]);
            }
        });
        
        // 移除空值字段
        Object.keys(processed).forEach(key => {
            if (processed[key] === '' || processed[key] === null || processed[key] === undefined) {
                delete processed[key];
            }
        });
        
        logger.log('订单数据预处理完成', 'info', { processed });
        return processed;
    }
    
    /**
     * 根据乘客人数推荐车型（优化版本，与Gemini服务保持一致）
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 推荐的车型ID
     */
    recommendCarType(passengerCount) {
        const count = parseInt(passengerCount);

        // 使用与GeminiService一致的车型映射
        const carTypeMapping = [
            { id: 5, name: '5 Seater', passengerLimit: 3 },
            { id: 15, name: '7 Seater MPV', passengerLimit: 5 },
            { id: 36, name: 'Alphard', passengerLimit: 6 },
            { id: 20, name: '10 Seater MPV/Van', passengerLimit: 7 },
            { id: 23, name: '14 Seater Van', passengerLimit: 10 }
        ];

        for (const carType of carTypeMapping) {
            if (count <= carType.passengerLimit) {
                logger.log('车型推荐', 'info', {
                    passengerCount: count,
                    recommended: carType
                });
                return carType.id;
            }
        }

        // 默认返回最大车型
        logger.log('使用最大车型', 'warning', { passengerCount: count });
        return 23;
    }

    /**
     * 获取允许的子分类列表（限制范围）
     * @returns {Array} 允许的子分类列表
     */
    getAllowedSubCategories() {
        return this.staticData.subCategories;
    }

    /**
     * 验证子分类ID是否在允许范围内
     * @param {number} subCategoryId - 子分类ID
     * @returns {boolean} 是否允许
     */
    isAllowedSubCategory(subCategoryId) {
        const allowedIds = [2, 3, 4]; // Pickup, Dropoff, Charter
        return allowedIds.includes(parseInt(subCategoryId));
    }
    
    /**
     * 验证订单数据
     * @param {object} orderData - 订单数据
     * @returns {object} 验证结果
     */
    validateOrderData(orderData) {
        const errors = {};
        const warnings = [];
        
        // 必需字段验证
        const requiredFields = [
            { field: 'sub_category_id', name: '子分类' },
            { field: 'ota_reference_number', name: 'OTA参考号' },
            { field: 'car_type_id', name: '车型' },
            { field: 'incharge_by_backend_user_id', name: '负责人' }
        ];
        
        requiredFields.forEach(({ field, name }) => {
            if (!orderData[field]) {
                errors[field] = [`${name}为必填项`];
            }
        });
        
        // 邮箱格式验证
        if (orderData.customer_email && !this.isValidEmail(orderData.customer_email)) {
            errors.customer_email = ['邮箱格式不正确'];
        }
        
        // 电话格式验证
        if (orderData.customer_contact && !this.isValidPhone(orderData.customer_contact)) {
            warnings.push('电话号码格式可能不正确');
        }
        
        // 日期验证
        if (orderData.date && !this.isValidDate(orderData.date)) {
            errors.date = ['日期格式不正确'];
        }
        
        // 数字字段验证
        ['passenger_number', 'luggage_number', 'ota_price', 'driver_fee', 'driver_collect'].forEach(field => {
            if (orderData[field] !== undefined && isNaN(Number(orderData[field]))) {
                errors[field] = [`${field}必须是数字`];
            }
        });
        
        const result = {
            isValid: Object.keys(errors).length === 0,
            errors,
            warnings
        };
        
        logger.logDataChange('订单验证', null, result, 'validation');
        return result;
    }
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 验证电话格式
     * @param {string} phone - 电话号码
     * @returns {boolean} 是否有效
     */
    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        return phoneRegex.test(phone);
    }
    
    /**
     * 验证日期格式
     * @param {string} date - 日期字符串
     * @returns {boolean} 是否有效
     */
    isValidDate(date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) return false;
        
        const dateObj = new Date(date);
        return dateObj instanceof Date && !isNaN(dateObj);
    }
    
    /**
     * 获取系统状态
     * @returns {object} 系统状态
     */
    getSystemStatus() {
        return {
            connected: appState.get('system.connected'),
            lastApiCall: appState.get('system.lastApiCall'),
            apiCallCount: appState.get('system.apiCallCount'),
            hasSystemData: appState.get('systemData.lastUpdated') !== null,
            authStatus: appState.get('auth.isLoggedIn')
        };
    }
}

// 创建全局API服务实例
const apiService = new ApiService();
export default apiService;