<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单预览与编辑 - 紧凑版</title>
    <link rel="stylesheet" href="order-preview-compact.css">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
</head>
<body>
    <div id="compactOrderApp" class="compact-order-app">
        <!-- 顶部工具栏 -->
        <header class="compact-header">
            <div class="header-left">
                <button class="back-btn" id="backBtn">←</button>
                <div class="order-info">
                    <span class="order-id" id="orderId">GMH-240315001</span>
                    <span class="order-status" id="orderStatus">待确认</span>
                </div>
            </div>
            <div class="header-right">
                <button class="edit-btn" id="editBtn">编辑</button>
                <button class="save-btn" id="saveBtn" style="display: none;">保存</button>
                <button class="reset-btn" id="resetBtn">重置</button>
            </div>
        </header>

        <!-- 主要内容区 - 三列布局 -->
        <main class="compact-main">
            <!-- 左列：基本信息 -->
            <section class="info-column basic-column">
                <div class="column-header">
                    <h3>📋 基本信息</h3>
                    <div class="smart-indicators">
                        <span class="indicator" id="basicIndicator">●</span>
                    </div>
                </div>
                
                <div class="info-grid">
                    <!-- 订单号 -->
                    <div class="field-group required">
                        <label>订单号</label>
                        <div class="field-content">
                            <span class="display-value" id="displayOtaRef">GMH-240315001</span>
                            <input type="text" class="edit-input" id="editOtaRef" value="GMH-240315001">
                        </div>
                    </div>

                    <!-- 服务类型 - 智能匹配 -->
                    <div class="field-group required">
                        <label>服务类型</label>
                        <div class="field-content">
                            <span class="display-value" id="displaySubCategory">接机服务</span>
                            <select class="edit-select smart-select" id="editSubCategory">
                                <option value="2" selected>接机服务</option>
                                <option value="3">送机服务</option>
                                <option value="4">包车服务</option>
                                <option value="25">包车（商铺）</option>
                            </select>
                            <small class="smart-hint" id="subCategoryHint">已智能匹配</small>
                        </div>
                    </div>

                    <!-- 负责人 -->
                    <div class="field-group required">
                        <label>负责人</label>
                        <div class="field-content">
                            <span class="display-value" id="displayIncharge">Jcy</span>
                            <select class="edit-select" id="editIncharge">
                                <option value="310" selected>Jcy</option>
                                <option value="311">opAnnie</option>
                                <option value="37">smw</option>
                                <option value="420">chongyoonlim</option>
                            </select>
                        </div>
                    </div>

                    <!-- 创建时间 -->
                    <div class="field-group">
                        <label>创建时间</label>
                        <div class="field-content">
                            <span class="display-value">2024-03-15 14:30</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 中列：行程信息 -->
            <section class="info-column trip-column">
                <div class="column-header">
                    <h3>🚗 行程信息</h3>
                    <div class="smart-indicators">
                        <span class="indicator" id="tripIndicator">●</span>
                    </div>
                </div>

                <!-- 路线可视化 -->
                <div class="route-visual">
                    <div class="route-point start">
                        <div class="point-marker">A</div>
                        <div class="point-details">
                            <div class="field-content">
                                <span class="display-value" id="displayPickup">KLIA2国际机场</span>
                                <input type="text" class="edit-input" id="editPickup" value="KLIA2国际机场" placeholder="上车地点">
                            </div>
                        </div>
                    </div>
                    
                    <div class="route-arrow">→</div>
                    
                    <div class="route-point end">
                        <div class="point-marker">B</div>
                        <div class="point-details">
                            <div class="field-content">
                                <span class="display-value" id="displayDestination">吉隆坡双子塔</span>
                                <input type="text" class="edit-input" id="editDestination" value="吉隆坡双子塔" placeholder="目的地">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间和车型 -->
                <div class="trip-details">
                    <div class="detail-row">
                        <div class="detail-item">
                            <label>日期</label>
                            <div class="field-content">
                                <span class="display-value" id="displayDate">2024-03-15</span>
                                <input type="date" class="edit-input" id="editDate" value="2024-03-15">
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>时间</label>
                            <div class="field-content">
                                <span class="display-value" id="displayTime">14:30</span>
                                <input type="time" class="edit-input" id="editTime" value="14:30">
                            </div>
                        </div>
                    </div>

                    <!-- 车型 - 智能推荐 -->
                    <div class="field-group required">
                        <label>车型</label>
                        <div class="field-content">
                            <span class="display-value" id="displayCarType">5座舒适型</span>
                            <select class="edit-select smart-select" id="editCarType">
                                <option value="5" selected>5座舒适型 (3人)</option>
                                <option value="15">7座MPV (5人)</option>
                                <option value="20">10座商务车 (7人)</option>
                                <option value="36">Alphard (6人)</option>
                            </select>
                            <small class="smart-hint" id="carTypeHint">根据人数推荐</small>
                        </div>
                    </div>

                    <!-- 人数和行李 -->
                    <div class="detail-row">
                        <div class="detail-item">
                            <label>乘客</label>
                            <div class="field-content">
                                <span class="display-value" id="displayPassenger">3人</span>
                                <input type="number" class="edit-input" id="editPassenger" value="3" min="1" max="50">
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>行李</label>
                            <div class="field-content">
                                <span class="display-value" id="displayLuggage">2件</span>
                                <input type="number" class="edit-input" id="editLuggage" value="2" min="0" max="50">
                            </div>
                        </div>
                    </div>

                    <!-- 行驶区域 - 智能匹配 -->
                    <div class="field-group">
                        <label>行驶区域</label>
                        <div class="field-content">
                            <span class="display-value" id="displayRegion">吉隆坡/雪兰莪</span>
                            <select class="edit-select smart-select" id="editRegion">
                                <option value="1" selected>吉隆坡/雪兰莪 (KL)</option>
                                <option value="2">槟城 (PNG)</option>
                                <option value="3">柔佛 (JB)</option>
                                <option value="4">沙巴 (SBH)</option>
                            </select>
                            <small class="smart-hint" id="regionHint">基于目的地匹配</small>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右列：客户与费用 -->
            <section class="info-column customer-column">
                <div class="column-header">
                    <h3>👤 客户信息</h3>
                    <div class="smart-indicators">
                        <span class="indicator" id="customerIndicator">●</span>
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="customer-info">
                    <div class="field-group">
                        <label>客户姓名</label>
                        <div class="field-content">
                            <span class="display-value" id="displayCustomerName">张三</span>
                            <input type="text" class="edit-input" id="editCustomerName" value="张三" placeholder="客户姓名">
                        </div>
                    </div>

                    <div class="field-group">
                        <label>联系电话</label>
                        <div class="field-content">
                            <span class="display-value" id="displayCustomerPhone">+60123456789</span>
                            <input type="tel" class="edit-input" id="editCustomerPhone" value="+60123456789" placeholder="联系电话">
                        </div>
                    </div>

                    <div class="field-group">
                        <label>客户邮箱</label>
                        <div class="field-content">
                            <span class="display-value" id="displayCustomerEmail"><EMAIL></span>
                            <input type="email" class="edit-input" id="editCustomerEmail" value="<EMAIL>" placeholder="客户邮箱">
                        </div>
                    </div>

                    <div class="field-group">
                        <label>航班信息</label>
                        <div class="field-content">
                            <span class="display-value" id="displayFlightInfo">MH123</span>
                            <input type="text" class="edit-input" id="editFlightInfo" value="MH123" placeholder="航班号">
                        </div>
                    </div>
                </div>

                <!-- 语言偏好 - 智能设置 -->
                <div class="field-group">
                    <label>语言偏好</label>
                    <div class="field-content">
                        <span class="display-value" id="displayLanguages">中文, 英文</span>
                        <select class="edit-select smart-select" id="editLanguages" multiple>
                            <option value="2" selected>英文 (EN)</option>
                            <option value="3">马来文 (MY)</option>
                            <option value="4" selected>中文 (CN)</option>
                        </select>
                        <small class="smart-hint" id="languageHint">基于客户信息推荐</small>
                    </div>
                </div>

                <!-- 费用信息 -->
                <div class="pricing-section">
                    <h4>💰 费用明细</h4>
                    <div class="pricing-grid">
                        <div class="price-item">
                            <label>OTA价格</label>
                            <div class="field-content">
                                <span class="display-value" id="displayOtaPrice">RM 120.00</span>
                                <input type="number" class="edit-input" id="editOtaPrice" value="120" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="price-item">
                            <label>司机费用</label>
                            <div class="field-content">
                                <span class="display-value" id="displayDriverFee">RM 100.00</span>
                                <input type="number" class="edit-input" id="editDriverFee" value="100" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="price-item">
                            <label>司机代收</label>
                            <div class="field-content">
                                <span class="display-value" id="displayDriverCollect">RM 20.00</span>
                                <input type="number" class="edit-input" id="editDriverCollect" value="20" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="price-total">
                            <label>总计</label>
                            <span class="total-amount" id="totalAmount">RM 120.00</span>
                        </div>
                    </div>
                </div>

                <!-- 特殊要求 -->
                <div class="requirements-section">
                    <h4>⭐ 特殊要求</h4>
                    <div class="requirements-grid">
                        <label class="checkbox-item">
                            <input type="checkbox" id="babyChair" checked>
                            <span class="checkbox-text">儿童座椅</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="tourGuide">
                            <span class="checkbox-text">导游服务</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="meetGreet">
                            <span class="checkbox-text">接机服务</span>
                        </label>
                    </div>
                    <div class="field-group">
                        <label>备注</label>
                        <div class="field-content">
                            <span class="display-value" id="displayExtraReq">需要中文司机</span>
                            <textarea class="edit-textarea" id="editExtraReq" rows="2" placeholder="其他要求...">需要中文司机</textarea>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部操作栏 -->
        <footer class="compact-footer">
            <div class="footer-left">
                <div class="validation-status" id="validationStatus">
                    <span class="status-icon">✓</span>
                    <span class="status-text">数据完整</span>
                </div>
            </div>
            <div class="footer-center">
                <button class="action-btn preview-btn" id="previewBtn">预览</button>
                <button class="action-btn validate-btn" id="validateBtn">验证</button>
            </div>
            <div class="footer-right">
                <button class="action-btn cancel-btn" id="cancelBtn">取消</button>
                <button class="action-btn confirm-btn" id="confirmBtn">确认订单</button>
            </div>
        </footer>

        <!-- 智能提示浮层 -->
        <div class="smart-tooltip" id="smartTooltip" style="display: none;">
            <div class="tooltip-content">
                <div class="tooltip-header">
                    <span class="tooltip-icon">🤖</span>
                    <span class="tooltip-title">智能建议</span>
                </div>
                <div class="tooltip-body" id="tooltipBody"></div>
            </div>
        </div>

        <!-- 快速搜索框 -->
        <div class="quick-search" id="quickSearch" style="display: none;">
            <input type="text" placeholder="快速搜索选项..." id="searchInput">
            <div class="search-results" id="searchResults"></div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <span class="loading-text">处理中...</span>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script type="module" src="order-preview-compact.js"></script>
</body>
</html>