@echo off
echo 正在启动OTA订单处理系统本地服务器...
echo.
echo 请选择启动方式:
echo 1. 使用Python HTTP服务器 (端口 8000)
echo 2. 使用Node.js http-server (端口 8080)
echo 3. 使用Vite开发服务器 (端口 5173)
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" goto python_server
if "%choice%"=="2" goto node_server
if "%choice%"=="3" goto vite_server
goto invalid_choice

:python_server
echo.
echo 启动Python HTTP服务器...
echo 访问地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.
python -m http.server 8000
goto end

:node_server
echo.
echo 安装并启动http-server...
npm install -g http-server
echo 访问地址: http://localhost:8080
echo 按 Ctrl+C 停止服务器
echo.
http-server -p 8080
goto end

:vite_server
echo.
echo 启动Vite开发服务器...
echo 访问地址: http://localhost:5173
echo 按 Ctrl+C 停止服务器
echo.
npm run dev
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
goto end

:end
pause
