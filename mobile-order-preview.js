/**
 * 移动端订单预览与编辑功能
 * 基于现有OTA系统架构的移动端优化实现
 */

import appState from './js/app-state.js';
import apiService from './js/api-service.js';
import logger from './js/logger.js';
import utils from './js/utils.js';

class MobileOrderPreview {
    constructor() {
        this.isEditMode = false;
        this.currentOrderData = {};
        this.originalOrderData = {};
        this.hasUnsavedChanges = false;
        this.expandedCards = new Set(['basic-info', 'trip-info']); // 默认展开的卡片
        
        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.toggleEditMode = this.toggleEditMode.bind(this);
        this.saveChanges = this.saveChanges.bind(this);
        this.handleCardExpand = this.handleCardExpand.bind(this);
        this.handleFieldChange = this.handleFieldChange.bind(this);
        this.handleFabMenu = this.handleFabMenu.bind(this);
    }
    
    /**
     * 初始化移动端订单预览
     */
    async init() {
        try {
            logger.logUserAction('移动端订单预览初始化');
            
            // 缓存DOM元素
            this.cacheElements();
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 加载订单数据
            await this.loadOrderData();
            
            // 初始化UI状态
            this.initializeUI();
            
            logger.logUserAction('移动端订单预览初始化完成');
            
        } catch (error) {
            logger.logError('移动端订单预览初始化失败', error);
            this.showToast('初始化失败，请刷新页面重试', 'error');
        }
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            // 主容器
            app: document.getElementById('mobileOrderApp'),
            
            // 头部控件
            backBtn: document.getElementById('backBtn'),
            editToggleBtn: document.getElementById('editToggleBtn'),
            saveBtn: document.getElementById('saveBtn'),
            
            // 状态显示
            orderStatus: document.getElementById('orderStatus'),
            orderId: document.getElementById('orderId'),
            
            // 卡片展开按钮
            expandBtns: document.querySelectorAll('.expand-btn'),
            
            // 底部操作
            cancelBtn: document.getElementById('cancelBtn'),
            confirmBtn: document.getElementById('confirmBtn'),
            
            // 浮动按钮
            mainFab: document.getElementById('mainFab'),
            fabMenu: document.getElementById('fabMenu'),
            subFabs: document.querySelectorAll('.sub-fab'),
            
            // 加载和提示
            loadingOverlay: document.getElementById('loadingOverlay'),
            toastContainer: document.getElementById('toastContainer'),
            
            // 可编辑字段
            editableFields: document.querySelectorAll('.editable-field'),
            checkboxes: document.querySelectorAll('.requirement-checkbox')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 头部按钮事件
        this.elements.backBtn?.addEventListener('click', this.handleBack.bind(this));
        this.elements.editToggleBtn?.addEventListener('click', this.toggleEditMode);
        this.elements.saveBtn?.addEventListener('click', this.saveChanges);
        
        // 卡片展开/折叠事件
        this.elements.expandBtns.forEach(btn => {
            btn.addEventListener('click', this.handleCardExpand);
        });
        
        // 底部操作按钮
        this.elements.cancelBtn?.addEventListener('click', this.handleCancel.bind(this));
        this.elements.confirmBtn?.addEventListener('click', this.handleConfirm.bind(this));
        
        // 浮动按钮事件
        this.elements.mainFab?.addEventListener('click', this.handleFabMenu);
        this.elements.subFabs.forEach(fab => {
            fab.addEventListener('click', this.handleSubFabAction.bind(this));
        });
        
        // 字段变更事件
        this.elements.editableFields.forEach(field => {
            const input = field.querySelector('.edit-input, .edit-select, .edit-textarea');
            if (input) {
                input.addEventListener('input', this.handleFieldChange);
                input.addEventListener('change', this.handleFieldChange);
            }
        });
        
        // 复选框事件
        this.elements.checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', this.handleFieldChange);
        });
        
        // 触摸事件优化
        this.setupTouchEvents();
        
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
    
    /**
     * 设置触摸事件优化
     */
    setupTouchEvents() {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (event) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 优化滚动性能
        const scrollContainer = this.elements.app.querySelector('.mobile-main');
        if (scrollContainer) {
            scrollContainer.addEventListener('touchstart', () => {
                scrollContainer.style.webkitOverflowScrolling = 'touch';
            });
        }
    }
    
    /**
     * 加载订单数据
     */
    async loadOrderData() {
        this.showLoading(true);
        
        try {
            // 从URL参数或应用状态获取订单ID
            const urlParams = utils.parseUrlParams();
            const orderId = urlParams.orderId || appState.get('currentOrder.id');
            
            if (orderId) {
                // 从API加载订单数据
                this.currentOrderData = await this.fetchOrderData(orderId);
            } else {
                // 使用当前订单数据或示例数据
                this.currentOrderData = appState.get('currentOrder.formData') || this.getExampleOrderData();
            }
            
            // 保存原始数据用于比较
            this.originalOrderData = utils.deepClone(this.currentOrderData);
            
            // 填充UI
            this.populateOrderData();
            
        } catch (error) {
            logger.logError('加载订单数据失败', error);
            this.showToast('加载订单数据失败', 'error');
            
            // 使用示例数据
            this.currentOrderData = this.getExampleOrderData();
            this.populateOrderData();
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 获取示例订单数据
     */
    getExampleOrderData() {
        return {
            ota_reference_number: 'GMH-240315001',
            sub_category_id: '2',
            car_type_id: '5',
            incharge_by_backend_user_id: '310',
            customer_name: '张三',
            customer_contact: '+60123456789',
            customer_email: '<EMAIL>',
            flight_info: 'MH123',
            pickup: 'KLIA2国际机场',
            destination: '吉隆坡双子塔',
            date: '2024-03-15',
            time: '14:30',
            passenger_number: 3,
            luggage_number: 2,
            ota_price: 120.00,
            driver_fee: 100.00,
            driver_collect: 20.00,
            baby_chair: true,
            tour_guide: false,
            meet_and_greet: false,
            extra_requirement: '需要中文司机',
            status: 'pending',
            created_at: '2024-03-15T14:30:00Z'
        };
    }
    
    /**
     * 填充订单数据到UI
     */
    populateOrderData() {
        try {
            // 更新状态显示
            this.updateStatusDisplay();
            
            // 填充可编辑字段
            this.elements.editableFields.forEach(field => {
                const fieldName = field.dataset.field;
                const value = this.currentOrderData[fieldName];
                
                if (value !== undefined) {
                    const displayValue = field.querySelector('.display-value');
                    const editInput = field.querySelector('.edit-input, .edit-select, .edit-textarea');
                    
                    if (displayValue) {
                        displayValue.textContent = this.formatDisplayValue(fieldName, value);
                    }
                    
                    if (editInput) {
                        editInput.value = value;
                    }
                }
            });
            
            // 填充复选框
            this.elements.checkboxes.forEach(checkbox => {
                const fieldName = checkbox.dataset.field;
                const value = this.currentOrderData[fieldName];
                checkbox.checked = Boolean(value);
            });
            
            // 更新费用计算
            this.updatePricingDisplay();
            
            logger.logUserAction('订单数据填充完成', { orderId: this.currentOrderData.ota_reference_number });
            
        } catch (error) {
            logger.logError('填充订单数据失败', error);
            this.showToast('数据显示异常', 'error');
        }
    }
    
    /**
     * 格式化显示值
     */
    formatDisplayValue(fieldName, value) {
        switch (fieldName) {
            case 'date':
                return utils.formatDate(new Date(value), 'YYYY-MM-DD');
            case 'time':
                return value;
            case 'passenger_number':
                return `${value}人`;
            case 'luggage_number':
                return `${value}件`;
            case 'ota_price':
            case 'driver_fee':
            case 'driver_collect':
                return `RM ${parseFloat(value).toFixed(2)}`;
            case 'sub_category_id':
                return this.getSubCategoryName(value);
            case 'car_type_id':
                return this.getCarTypeName(value);
            case 'incharge_by_backend_user_id':
                return this.getBackendUserName(value);
            default:
                return String(value);
        }
    }
    
    /**
     * 获取子分类名称
     */
    getSubCategoryName(id) {
        const subCategories = appState.get('systemData.subCategories') || [];
        const category = subCategories.find(cat => cat.id == id);
        return category ? category.name : '接机服务';
    }
    
    /**
     * 获取车型名称
     */
    getCarTypeName(id) {
        const carTypes = appState.get('systemData.carTypes') || [];
        const carType = carTypes.find(car => car.id == id);
        return carType ? carType.name : '5座舒适型';
    }
    
    /**
     * 获取后台用户名称
     */
    getBackendUserName(id) {
        const users = appState.get('systemData.backendUsers') || [];
        const user = users.find(u => u.id == id);
        return user ? user.name : 'Jcy';
    }
    
    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        const statusElement = this.elements.orderStatus?.querySelector('.status-text');
        const orderIdElement = this.elements.orderId;
        
        if (statusElement) {
            const statusMap = {
                'pending': '待确认',
                'confirmed': '已确认',
                'in_progress': '进行中',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            statusElement.textContent = statusMap[this.currentOrderData.status] || '待确认';
        }
        
        if (orderIdElement) {
            orderIdElement.textContent = this.currentOrderData.ota_reference_number || 'GMH-240315001';
        }
    }
    
    /**
     * 更新费用显示
     */
    updatePricingDisplay() {
        const totalAmountElement = document.querySelector('.total-amount');
        if (totalAmountElement) {
            const total = parseFloat(this.currentOrderData.ota_price || 0);
            totalAmountElement.textContent = `RM ${total.toFixed(2)}`;
        }
    }
    
    /**
     * 初始化UI状态
     */
    initializeUI() {
        // 设置默认展开的卡片
        this.expandedCards.forEach(cardId => {
            const cardContent = document.getElementById(cardId);
            if (cardContent) {
                cardContent.classList.remove('collapsed');
            }
        });
        
        // 更新展开按钮状态
        this.updateExpandButtons();
    }
    
    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        
        if (this.isEditMode) {
            this.elements.app.classList.add('edit-mode');
            this.elements.editToggleBtn.style.display = 'none';
            this.elements.saveBtn.style.display = 'flex';
            
            // 展开所有卡片以便编辑
            this.expandAllCards();
            
            logger.logUserAction('进入编辑模式');
            this.showToast('编辑模式已启用', 'info');
        } else {
            this.exitEditMode();
        }
    }
    
    /**
     * 退出编辑模式
     */
    exitEditMode() {
        this.elements.app.classList.remove('edit-mode');
        this.elements.editToggleBtn.style.display = 'flex';
        this.elements.saveBtn.style.display = 'none';
        this.isEditMode = false;
        
        // 如果有未保存的更改，提示用户
        if (this.hasUnsavedChanges) {
            this.showConfirm(
                '有未保存的更改，是否保存？',
                () => this.saveChanges(),
                () => {
                    this.discardChanges();
                    logger.logUserAction('退出编辑模式，放弃更改');
                }
            );
        } else {
            logger.logUserAction('退出编辑模式');
        }
    }
    
    /**
     * 展开所有卡片
     */
    expandAllCards() {
        const allCardContents = document.querySelectorAll('.card-content');
        allCardContents.forEach(content => {
            content.classList.remove('collapsed');
            this.expandedCards.add(content.id);
        });
        this.updateExpandButtons();
    }
    
    /**
     * 保存更改
     */
    async saveChanges() {
        try {
            this.showLoading(true);
            
            // 收集表单数据
            const updatedData = this.collectFormData();
            
            // 验证数据
            const validation = this.validateOrderData(updatedData);
            if (!validation.isValid) {
                this.showValidationErrors(validation.errors);
                return;
            }
            
            // 保存到服务器
            await this.saveOrderData(updatedData);
            
            // 更新本地数据
            this.currentOrderData = updatedData;
            this.originalOrderData = utils.deepClone(updatedData);
            this.hasUnsavedChanges = false;
            
            // 退出编辑模式
            this.exitEditMode();
            
            // 更新显示
            this.populateOrderData();
            
            logger.logUserAction('订单数据保存成功', { orderId: updatedData.ota_reference_number });
            this.showToast('保存成功', 'success');
            
        } catch (error) {
            logger.logError('保存订单数据失败', error);
            this.showToast('保存失败，请重试', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    /**
     * 收集表单数据
     */
    collectFormData() {
        const data = { ...this.currentOrderData };
        
        // 收集可编辑字段
        this.elements.editableFields.forEach(field => {
            const fieldName = field.dataset.field;
            const editInput = field.querySelector('.edit-input, .edit-select, .edit-textarea');
            
            if (editInput && editInput.value !== undefined) {
                data[fieldName] = editInput.value;
            }
        });
        
        // 收集复选框
        this.elements.checkboxes.forEach(checkbox => {
            const fieldName = checkbox.dataset.field;
            data[fieldName] = checkbox.checked;
        });
        
        return data;
    }
    
    /**
     * 验证订单数据
     */
    validateOrderData(data) {
        const errors = {};
        
        // 必填字段验证
        const requiredFields = [
            'ota_reference_number',
            'sub_category_id',
            'car_type_id',
            'incharge_by_backend_user_id'
        ];
        
        requiredFields.forEach(field => {
            if (!data[field] || String(data[field]).trim() === '') {
                errors[field] = '此字段为必填项';
            }
        });
        
        // 邮箱格式验证
        if (data.customer_email && !utils.isValidEmail(data.customer_email)) {
            errors.customer_email = '邮箱格式不正确';
        }
        
        // 电话格式验证
        if (data.customer_contact && !utils.isValidPhone(data.customer_contact)) {
            errors.customer_contact = '电话格式不正确';
        }
        
        // 日期验证
        if (data.date) {
            const selectedDate = new Date(data.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                errors.date = '不能选择过去的日期';
            }
        }
        
        // 数字字段验证
        ['passenger_number', 'luggage_number', 'ota_price', 'driver_fee', 'driver_collect'].forEach(field => {
            if (data[field] !== undefined && data[field] !== '' && isNaN(Number(data[field]))) {
                errors[field] = '必须是有效数字';
            }
        });
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
    
    /**
     * 显示验证错误
     */
    showValidationErrors(errors) {
        const errorMessages = Object.entries(errors).map(([field, message]) => {
            return `${this.getFieldDisplayName(field)}: ${message}`;
        });
        
        this.showToast(`验证失败:\n${errorMessages.join('\n')}`, 'error');
        
        // 高亮错误字段
        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`[data-field="${fieldName}"]`);
            if (field) {
                field.classList.add('error');
                setTimeout(() => field.classList.remove('error'), 3000);
            }
        });
    }
    
    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(fieldName) {
        const fieldNames = {
            'ota_reference_number': '订单号',
            'sub_category_id': '服务类型',
            'car_type_id': '车型',
            'incharge_by_backend_user_id': '负责人',
            'customer_name': '客户姓名',
            'customer_contact': '联系电话',
            'customer_email': '客户邮箱',
            'pickup': '上车地点',
            'destination': '目的地',
            'date': '日期',
            'time': '时间',
            'passenger_number': '乘客人数',
            'luggage_number': '行李件数'
        };
        return fieldNames[fieldName] || fieldName;
    }
    
    /**
     * 处理卡片展开/折叠
     */
    handleCardExpand(event) {
        const button = event.currentTarget;
        const targetId = button.dataset.target;
        const cardContent = document.getElementById(targetId);
        
        if (cardContent) {
            const isCollapsed = cardContent.classList.contains('collapsed');
            
            if (isCollapsed) {
                cardContent.classList.remove('collapsed');
                this.expandedCards.add(targetId);
            } else {
                cardContent.classList.add('collapsed');
                this.expandedCards.delete(targetId);
            }
            
            this.updateExpandButtons();
            
            logger.logUserAction(`卡片${isCollapsed ? '展开' : '折叠'}`, { cardId: targetId });
        }
    }
    
    /**
     * 更新展开按钮状态
     */
    updateExpandButtons() {
        this.elements.expandBtns.forEach(btn => {
            const targetId = btn.dataset.target;
            const cardContent = document.getElementById(targetId);
            const icon = btn.querySelector('.expand-icon');
            
            if (cardContent && icon) {
                const isCollapsed = cardContent.classList.contains('collapsed');
                icon.style.transform = isCollapsed ? 'rotate(-90deg)' : 'rotate(0deg)';
            }
        });
    }
    
    /**
     * 处理字段变更
     */
    handleFieldChange(event) {
        const field = event.target.closest('.editable-field') || event.target.closest('.requirement-item');
        const fieldName = field?.dataset?.field || event.target.dataset.field;
        
        if (fieldName) {
            const oldValue = this.currentOrderData[fieldName];
            const newValue = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
            
            if (oldValue !== newValue) {
                this.hasUnsavedChanges = true;
                logger.logDataChange(fieldName, oldValue, newValue, 'mobile_edit');
                
                // 实时更新显示值
                if (event.target.type !== 'checkbox') {
                    const displayValue = field.querySelector('.display-value');
                    if (displayValue) {
                        displayValue.textContent = this.formatDisplayValue(fieldName, newValue);
                    }
                }
                
                // 特殊处理：乘客人数变化时推荐车型
                if (fieldName === 'passenger_number') {
                    this.handlePassengerNumberChange(parseInt(newValue));
                }
                
                // 更新费用计算
                if (['ota_price', 'driver_fee', 'driver_collect'].includes(fieldName)) {
                    this.updatePricingCalculation();
                }
            }
        }
    }
    
    /**
     * 处理乘客人数变化
     */
    handlePassengerNumberChange(passengerCount) {
        if (!isNaN(passengerCount) && passengerCount > 0) {
            const recommendedCarType = apiService.recommendCarType(passengerCount);
            const carTypeField = document.querySelector('[data-field="car_type_id"]');
            const carTypeSelect = carTypeField?.querySelector('.edit-select');
            
            if (carTypeSelect) {
                carTypeSelect.value = recommendedCarType;
                
                // 更新显示值
                const displayValue = carTypeField.querySelector('.display-value');
                if (displayValue) {
                    displayValue.textContent = this.getCarTypeName(recommendedCarType);
                }
                
                this.showToast(`已推荐适合${passengerCount}人的车型`, 'info');
            }
            
            // 自动设置行李数量建议
            const luggageField = document.querySelector('[data-field="luggage_number"]');
            const luggageInput = luggageField?.querySelector('.edit-input');
            if (luggageInput && !luggageInput.value) {
                const suggestedLuggage = Math.min(passengerCount, 4);
                luggageInput.value = suggestedLuggage;
                
                const displayValue = luggageField.querySelector('.display-value');
                if (displayValue) {
                    displayValue.textContent = `${suggestedLuggage}件`;
                }
            }
        }
    }
    
    /**
     * 更新费用计算
     */
    updatePricingCalculation() {
        // 这里可以添加复杂的费用计算逻辑
        // 目前只是简单更新总计显示
        setTimeout(() => {
            this.updatePricingDisplay();
        }, 100);
    }
    
    /**
     * 处理浮动按钮菜单
     */
    handleFabMenu() {
        const fabContainer = this.elements.mainFab.parentElement;
        const isActive = fabContainer.classList.contains('active');
        
        if (isActive) {
            fabContainer.classList.remove('active');
        } else {
            fabContainer.classList.add('active');
        }
        
        logger.logUserAction(`浮动菜单${isActive ? '关闭' : '打开'}`);
    }
    
    /**
     * 处理子浮动按钮操作
     */
    handleSubFabAction(event) {
        const action = event.currentTarget.dataset.action;
        
        switch (action) {
            case 'duplicate':
                this.duplicateOrder();
                break;
            case 'share':
                this.shareOrder();
                break;
            case 'history':
                this.showOrderHistory();
                break;
        }
        
        // 关闭菜单
        this.elements.mainFab.parentElement.classList.remove('active');
    }
    
    /**
     * 复制订单
     */
    async duplicateOrder() {
        try {
            const orderText = this.generateOrderText();
            await utils.copyToClipboard(orderText);
            this.showToast('订单信息已复制到剪贴板', 'success');
            logger.logUserAction('复制订单信息');
        } catch (error) {
            logger.logError('复制订单失败', error);
            this.showToast('复制失败', 'error');
        }
    }
    
    /**
     * 分享订单
     */
    async shareOrder() {
        try {
            const orderText = this.generateOrderText();
            
            if (navigator.share) {
                await navigator.share({
                    title: '订单信息',
                    text: orderText
                });
                logger.logUserAction('分享订单信息');
            } else {
                // 降级到复制
                await utils.copyToClipboard(orderText);
                this.showToast('订单信息已复制，可手动分享', 'info');
            }
        } catch (error) {
            logger.logError('分享订单失败', error);
            this.showToast('分享失败', 'error');
        }
    }
    
    /**
     * 生成订单文本
     */
    generateOrderText() {
        const data = this.currentOrderData;
        return `
订单号: ${data.ota_reference_number}
客户: ${data.customer_name}
电话: ${data.customer_contact}
路线: ${data.pickup} → ${data.destination}
时间: ${data.date} ${data.time}
车型: ${this.getCarTypeName(data.car_type_id)}
人数: ${data.passenger_number}人
价格: RM ${data.ota_price}
        `.trim();
    }
    
    /**
     * 显示订单历史
     */
    showOrderHistory() {
        // 这里可以实现订单历史功能
        this.showToast('订单历史功能开发中', 'info');
        logger.logUserAction('查看订单历史');
    }
    
    /**
     * 处理返回
     */
    handleBack() {
        if (this.hasUnsavedChanges) {
            this.showConfirm(
                '有未保存的更改，确定要离开吗？',
                () => {
                    logger.logUserAction('返回上一页，放弃更改');
                    this.navigateBack();
                }
            );
        } else {
            this.navigateBack();
        }
    }
    
    /**
     * 导航返回
     */
    navigateBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = '/';
        }
    }
    
    /**
     * 处理取消
     */
    handleCancel() {
        this.showConfirm(
            '确定要取消此订单吗？',
            async () => {
                try {
                    await this.cancelOrder();
                    this.showToast('订单已取消', 'success');
                    logger.logUserAction('取消订单', { orderId: this.currentOrderData.ota_reference_number });
                } catch (error) {
                    logger.logError('取消订单失败', error);
                    this.showToast('取消失败', 'error');
                }
            }
        );
    }
    
    /**
     * 处理确认
     */
    handleConfirm() {
        this.showConfirm(
            '确定要确认此订单吗？',
            async () => {
                try {
                    await this.confirmOrder();
                    this.showToast('订单已确认', 'success');
                    logger.logUserAction('确认订单', { orderId: this.currentOrderData.ota_reference_number });
                } catch (error) {
                    logger.logError('确认订单失败', error);
                    this.showToast('确认失败', 'error');
                }
            }
        );
    }
    
    /**
     * 处理键盘事件
     */
    handleKeyboard(event) {
        // ESC键退出编辑模式
        if (event.key === 'Escape' && this.isEditMode) {
            this.toggleEditMode();
        }
        
        // Ctrl+S保存
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            if (this.isEditMode) {
                this.saveChanges();
            }
        }
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.visibilityState === 'hidden' && this.hasUnsavedChanges) {
            // 页面隐藏时自动保存草稿
            this.saveDraft();
        }
    }
    
    /**
     * 保存草稿
     */
    saveDraft() {
        try {
            const draftData = this.collectFormData();
            localStorage.setItem('mobile_order_draft', JSON.stringify(draftData));
            logger.logUserAction('自动保存草稿');
        } catch (error) {
            logger.logError('保存草稿失败', error);
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading(show) {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }
    
    /**
     * 显示提示消息
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        this.elements.toastContainer.appendChild(toast);
        
        // 显示动画
        setTimeout(() => toast.classList.add('show'), 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
    
    /**
     * 显示确认对话框
     */
    showConfirm(message, onConfirm, onCancel = null) {
        const confirmed = confirm(message);
        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
    }
    
    /**
     * API相关方法（模拟实现）
     */
    async fetchOrderData(orderId) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.getExampleOrderData());
            }, 1000);
        });
    }
    
    async saveOrderData(data) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({ success: true });
            }, 1500);
        });
    }
    
    async cancelOrder() {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                this.currentOrderData.status = 'cancelled';
                this.updateStatusDisplay();
                resolve({ success: true });
            }, 1000);
        });
    }
    
    async confirmOrder() {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                this.currentOrderData.status = 'confirmed';
                this.updateStatusDisplay();
                resolve({ success: true });
            }, 1000);
        });
    }
    
    /**
     * 放弃更改
     */
    discardChanges() {
        this.currentOrderData = utils.deepClone(this.originalOrderData);
        this.hasUnsavedChanges = false;
        this.populateOrderData();
    }
}

// 初始化移动端订单预览
const mobileOrderPreview = new MobileOrderPreview();

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => mobileOrderPreview.init());
} else {
    mobileOrderPreview.init();
}

// 导出实例
export default mobileOrderPreview;