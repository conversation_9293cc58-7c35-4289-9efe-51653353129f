# CORS问题解决方案

## 🚨 问题描述

您遇到的CORS错误是因为直接在浏览器中打开HTML文件（file://协议），现代浏览器不允许从file://协议加载ES6模块。

## 🔧 解决方案

### 方法1：使用批处理脚本（推荐）

1. **双击运行** `start-server.bat` 文件
2. **选择启动方式**：
   - 选择 `1` - Python HTTP服务器（最简单）
   - 选择 `2` - Node.js http-server
   - 选择 `3` - Vite开发服务器（功能最全）

### 方法2：手动启动Python服务器

```bash
# 在项目目录下打开命令行，运行：
python -m http.server 8000

# 然后在浏览器访问：
http://localhost:8000
```

### 方法3：使用Node.js http-server

```bash
# 安装http-server（全局）
npm install -g http-server

# 启动服务器
http-server -p 8080

# 在浏览器访问：
http://localhost:8080
```

### 方法4：使用Vite开发服务器

```bash
# 确保依赖已安装
npm install

# 启动Vite开发服务器
npm run dev

# 在浏览器访问：
http://localhost:5173
```

## 🌐 访问地址

启动服务器后，在浏览器中访问以下地址之一：

- **Python服务器**: http://localhost:8000
- **http-server**: http://localhost:8080  
- **Vite服务器**: http://localhost:5173

## ✅ 验证步骤

1. 启动任一服务器
2. 在浏览器中访问对应地址
3. 确认页面正常加载，无CORS错误
4. 测试登录功能：
   - 邮箱：<EMAIL>
   - 密码：Gomyhire@123456
5. 测试订单解析功能

## 🔍 故障排除

### 如果Python命令不可用：
- 确保已安装Python 3.x
- 或使用其他方法

### 如果npm命令不可用：
- 确保已安装Node.js
- 或使用Python方法

### 如果端口被占用：
- 更改端口号，例如：
  ```bash
  python -m http.server 8001
  http-server -p 8081
  ```

## 📝 注意事项

1. **开发环境**: 这些服务器仅用于开发测试
2. **防火墙**: 可能需要允许相应端口通过防火墙
3. **停止服务器**: 在命令行中按 `Ctrl+C` 停止服务器
4. **生产部署**: 生产环境请使用专业的Web服务器

## 🚀 推荐使用

- **快速测试**: 使用Python HTTP服务器
- **开发调试**: 使用Vite开发服务器（支持热重载）
- **功能测试**: 任一方法都可以正常运行系统
