/* 移动端订单预览样式 - 针对375px-414px屏幕优化 */

/* CSS变量定义 */
:root {
  /* 移动端专用颜色系统 */
  --mobile-primary: #3B82F6;
  --mobile-primary-dark: #2563EB;
  --mobile-secondary: #6B7280;
  --mobile-success: #10B981;
  --mobile-warning: #F59E0B;
  --mobile-error: #EF4444;
  
  /* 背景色 */
  --mobile-bg-primary: #FFFFFF;
  --mobile-bg-secondary: #F8FAFC;
  --mobile-bg-card: #FFFFFF;
  --mobile-bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 文字色 */
  --mobile-text-primary: #1F2937;
  --mobile-text-secondary: #6B7280;
  --mobile-text-tertiary: #9CA3AF;
  --mobile-text-inverse: #FFFFFF;
  
  /* 边框色 */
  --mobile-border-light: #E5E7EB;
  --mobile-border-medium: #D1D5DB;
  --mobile-border-dark: #9CA3AF;
  
  /* 阴影 */
  --mobile-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --mobile-shadow-md: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  --mobile-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* 移动端专用间距 */
  --mobile-space-xs: 4px;
  --mobile-space-sm: 8px;
  --mobile-space-md: 12px;
  --mobile-space-lg: 16px;
  --mobile-space-xl: 20px;
  --mobile-space-2xl: 24px;
  
  /* 移动端字体大小 */
  --mobile-text-xs: 10px;
  --mobile-text-sm: 12px;
  --mobile-text-base: 14px;
  --mobile-text-lg: 16px;
  --mobile-text-xl: 18px;
  --mobile-text-2xl: 20px;
  
  /* 移动端圆角 */
  --mobile-radius-sm: 4px;
  --mobile-radius-md: 6px;
  --mobile-radius-lg: 8px;
  --mobile-radius-xl: 12px;
  
  /* 移动端过渡 */
  --mobile-transition-fast: 150ms ease-out;
  --mobile-transition-normal: 250ms ease-out;
  
  /* 移动端触摸目标最小尺寸 */
  --mobile-touch-target: 44px;
  
  /* 移动端布局尺寸 */
  --mobile-header-height: 56px;
  --mobile-footer-height: 72px;
  --mobile-fab-size: 56px;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--mobile-text-base);
  line-height: 1.4;
  color: var(--mobile-text-primary);
  background-color: var(--mobile-bg-secondary);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.mobile-order-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 顶部导航栏 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--mobile-header-height);
  background: var(--mobile-bg-primary);
  border-bottom: 1px solid var(--mobile-border-light);
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--mobile-space-lg);
}

.back-btn {
  width: var(--mobile-touch-target);
  height: var(--mobile-touch-target);
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--mobile-radius-lg);
  transition: background-color var(--mobile-transition-fast);
}

.back-btn:active {
  background-color: var(--mobile-bg-secondary);
}

.back-btn .icon {
  font-size: var(--mobile-text-xl);
  color: var(--mobile-text-primary);
}

.page-title {
  font-size: var(--mobile-text-lg);
  font-weight: 600;
  color: var(--mobile-text-primary);
  flex: 1;
  text-align: center;
  margin: 0 var(--mobile-space-md);
}

.header-actions {
  display: flex;
  gap: var(--mobile-space-sm);
}

.edit-toggle-btn,
.save-btn {
  width: var(--mobile-touch-target);
  height: var(--mobile-touch-target);
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--mobile-radius-lg);
  transition: all var(--mobile-transition-fast);
}

.edit-toggle-btn:active,
.save-btn:active {
  background-color: var(--mobile-bg-secondary);
  transform: scale(0.95);
}

.save-btn {
  background-color: var(--mobile-primary);
  color: var(--mobile-text-inverse);
}

.save-btn:active {
  background-color: var(--mobile-primary-dark);
}

/* 主要内容区 */
.mobile-main {
  flex: 1;
  padding-top: var(--mobile-header-height);
  padding-bottom: var(--mobile-footer-height);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--mobile-space-lg);
  background: var(--mobile-bg-primary);
  border-bottom: 1px solid var(--mobile-border-light);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-sm);
  padding: var(--mobile-space-sm) var(--mobile-space-md);
  background: var(--mobile-bg-secondary);
  border-radius: var(--mobile-radius-xl);
}

.status-icon {
  font-size: var(--mobile-text-sm);
}

.status-text {
  font-size: var(--mobile-text-sm);
  font-weight: 500;
  color: var(--mobile-text-secondary);
}

.order-id {
  font-size: var(--mobile-text-sm);
  font-weight: 600;
  color: var(--mobile-text-primary);
  font-family: 'Courier New', monospace;
}

/* 卡片容器 */
.card-container {
  padding: var(--mobile-space-sm);
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-sm);
}

/* 信息卡片 */
.info-card {
  background: var(--mobile-bg-card);
  border-radius: var(--mobile-radius-lg);
  box-shadow: var(--mobile-shadow-sm);
  border: 1px solid var(--mobile-border-light);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: var(--mobile-space-lg);
  background: var(--mobile-bg-secondary);
  border-bottom: 1px solid var(--mobile-border-light);
  min-height: var(--mobile-touch-target);
}

.card-icon {
  font-size: var(--mobile-text-lg);
  margin-right: var(--mobile-space-md);
}

.card-title {
  font-size: var(--mobile-text-base);
  font-weight: 600;
  color: var(--mobile-text-primary);
  flex: 1;
}

.expand-btn {
  width: var(--mobile-touch-target);
  height: var(--mobile-touch-target);
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--mobile-radius-md);
  transition: all var(--mobile-transition-fast);
}

.expand-btn:active {
  background-color: var(--mobile-border-light);
  transform: scale(0.9);
}

.expand-icon {
  font-size: var(--mobile-text-sm);
  color: var(--mobile-text-secondary);
  transition: transform var(--mobile-transition-fast);
}

.card-content.collapsed + .card-header .expand-icon {
  transform: rotate(-90deg);
}

.card-content {
  padding: var(--mobile-space-lg);
  transition: all var(--mobile-transition-normal);
}

.card-content.collapsed {
  display: none;
}

/* 基本信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--mobile-space-lg);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-xs);
}

.info-item label {
  font-size: var(--mobile-text-xs);
  font-weight: 500;
  color: var(--mobile-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: var(--mobile-text-sm);
  color: var(--mobile-text-primary);
  font-weight: 500;
}

/* 可编辑字段 */
.editable-field {
  position: relative;
}

.display-value {
  font-size: var(--mobile-text-sm);
  color: var(--mobile-text-primary);
  font-weight: 500;
  display: block;
  padding: var(--mobile-space-sm);
  border-radius: var(--mobile-radius-sm);
  transition: background-color var(--mobile-transition-fast);
}

.edit-input,
.edit-select,
.edit-textarea {
  width: 100%;
  padding: var(--mobile-space-sm);
  border: 1px solid var(--mobile-border-medium);
  border-radius: var(--mobile-radius-sm);
  font-size: var(--mobile-text-sm);
  background: var(--mobile-bg-primary);
  color: var(--mobile-text-primary);
  display: none;
  transition: border-color var(--mobile-transition-fast);
}

.edit-input:focus,
.edit-select:focus,
.edit-textarea:focus {
  outline: none;
  border-color: var(--mobile-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 编辑模式 */
.mobile-order-app.edit-mode .display-value {
  display: none;
}

.mobile-order-app.edit-mode .edit-input,
.mobile-order-app.edit-mode .edit-select,
.mobile-order-app.edit-mode .edit-textarea {
  display: block;
}

.mobile-order-app.edit-mode .display-value {
  background-color: var(--mobile-bg-secondary);
}

/* 路线可视化 */
.route-visual {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-sm);
  margin-bottom: var(--mobile-space-lg);
}

.route-point {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-md);
}

.point-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--mobile-bg-secondary);
  border-radius: 50%;
  font-size: var(--mobile-text-sm);
  flex-shrink: 0;
}

.point-info {
  flex: 1;
}

.point-info .display-value {
  font-size: var(--mobile-text-sm);
  font-weight: 600;
  color: var(--mobile-text-primary);
  padding: 0;
}

.point-label {
  font-size: var(--mobile-text-xs);
  color: var(--mobile-text-secondary);
  display: block;
  margin-top: var(--mobile-space-xs);
}

.route-line {
  display: flex;
  justify-content: center;
  margin: var(--mobile-space-xs) 0;
}

.route-dots {
  color: var(--mobile-text-tertiary);
  font-size: var(--mobile-text-sm);
  letter-spacing: 2px;
}

/* 行程详情 */
.trip-details {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-lg);
}

.detail-row {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-md);
}

.detail-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--mobile-text-sm);
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
}

.detail-content label {
  font-size: var(--mobile-text-xs);
  color: var(--mobile-text-secondary);
  display: block;
  margin-bottom: var(--mobile-space-xs);
  text-transform: uppercase;
  font-weight: 500;
}

.datetime-group {
  display: flex;
  gap: var(--mobile-space-md);
}

.datetime-group .editable-field {
  flex: 1;
}

.passenger-luggage {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-sm);
}

.passenger-luggage .editable-field {
  flex: 1;
}

.separator {
  color: var(--mobile-text-tertiary);
  font-weight: 500;
}

/* 客户信息 */
.customer-details {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-lg);
}

.customer-row {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-md);
}

.customer-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--mobile-text-sm);
  flex-shrink: 0;
}

.customer-content {
  flex: 1;
}

.customer-content label {
  font-size: var(--mobile-text-xs);
  color: var(--mobile-text-secondary);
  display: block;
  margin-bottom: var(--mobile-space-xs);
  text-transform: uppercase;
  font-weight: 500;
}

/* 费用明细 */
.pricing-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-md);
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--mobile-space-sm) 0;
}

.price-row:not(:last-child) {
  border-bottom: 1px solid var(--mobile-border-light);
}

.price-row.total {
  border-top: 2px solid var(--mobile-border-medium);
  padding-top: var(--mobile-space-md);
  margin-top: var(--mobile-space-sm);
}

.price-label {
  font-size: var(--mobile-text-sm);
  color: var(--mobile-text-secondary);
}

.price-row.total .price-label {
  font-weight: 600;
  color: var(--mobile-text-primary);
}

.price-value {
  font-size: var(--mobile-text-sm);
  font-weight: 600;
  color: var(--mobile-text-primary);
}

.total-amount {
  font-size: var(--mobile-text-lg);
  color: var(--mobile-primary);
}

/* 特殊要求 */
.requirements-list {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-lg);
}

.requirement-item {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--mobile-space-md);
  cursor: pointer;
  width: 100%;
  min-height: var(--mobile-touch-target);
}

.requirement-checkbox {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--mobile-border-medium);
  border-radius: var(--mobile-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--mobile-transition-fast);
  flex-shrink: 0;
}

.requirement-checkbox:checked + .checkbox-custom {
  background-color: var(--mobile-primary);
  border-color: var(--mobile-primary);
}

.requirement-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-size: var(--mobile-text-xs);
  font-weight: bold;
}

.requirement-text {
  font-size: var(--mobile-text-sm);
  color: var(--mobile-text-primary);
}

.requirement-note {
  margin-top: var(--mobile-space-md);
  padding-top: var(--mobile-space-md);
  border-top: 1px solid var(--mobile-border-light);
}

.requirement-note label {
  font-size: var(--mobile-text-xs);
  color: var(--mobile-text-secondary);
  display: block;
  margin-bottom: var(--mobile-space-sm);
  text-transform: uppercase;
  font-weight: 500;
}

/* 底部操作栏 */
.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--mobile-footer-height);
  background: var(--mobile-bg-primary);
  border-top: 1px solid var(--mobile-border-light);
  z-index: 100;
  backdrop-filter: blur(10px);
}

.footer-actions {
  height: 100%;
  display: flex;
  align-items: center;
  gap: var(--mobile-space-md);
  padding: var(--mobile-space-lg);
}

.action-btn {
  flex: 1;
  height: var(--mobile-touch-target);
  border: none;
  border-radius: var(--mobile-radius-lg);
  font-size: var(--mobile-text-base);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--mobile-space-sm);
  transition: all var(--mobile-transition-fast);
  cursor: pointer;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: var(--mobile-bg-secondary);
  color: var(--mobile-text-secondary);
  border: 1px solid var(--mobile-border-medium);
}

.action-btn.secondary:active {
  background: var(--mobile-border-light);
}

.action-btn.primary {
  background: var(--mobile-primary);
  color: var(--mobile-text-inverse);
}

.action-btn.primary:active {
  background: var(--mobile-primary-dark);
}

.btn-icon {
  font-size: var(--mobile-text-lg);
}

.btn-text {
  font-size: var(--mobile-text-base);
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: calc(var(--mobile-footer-height) + var(--mobile-space-lg));
  right: var(--mobile-space-lg);
  z-index: 200;
}

.fab {
  width: var(--mobile-fab-size);
  height: var(--mobile-fab-size);
  border: none;
  border-radius: 50%;
  background: var(--mobile-primary);
  color: var(--mobile-text-inverse);
  box-shadow: var(--mobile-shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--mobile-transition-fast);
  cursor: pointer;
}

.fab:active {
  transform: scale(0.9);
  box-shadow: var(--mobile-shadow-md);
}

.fab-icon {
  font-size: var(--mobile-text-xl);
}

.fab-menu {
  position: absolute;
  bottom: calc(100% + var(--mobile-space-md));
  right: 0;
  display: flex;
  flex-direction: column;
  gap: var(--mobile-space-md);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all var(--mobile-transition-normal);
}

.fab-container.active .fab-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.sub-fab {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: var(--mobile-bg-primary);
  color: var(--mobile-text-primary);
  box-shadow: var(--mobile-shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all var(--mobile-transition-fast);
}

.sub-fab:active {
  transform: scale(0.9);
}

.sub-fab .fab-icon {
  font-size: var(--mobile-text-base);
}

.fab-label {
  position: absolute;
  right: calc(100% + var(--mobile-space-sm));
  top: 50%;
  transform: translateY(-50%);
  background: var(--mobile-text-primary);
  color: var(--mobile-text-inverse);
  padding: var(--mobile-space-xs) var(--mobile-space-sm);
  border-radius: var(--mobile-radius-sm);
  font-size: var(--mobile-text-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--mobile-transition-fast);
}

.sub-fab:hover .fab-label {
  opacity: 1;
  visibility: visible;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--mobile-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--mobile-space-lg);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--mobile-border-light);
  border-top: 3px solid var(--mobile-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--mobile-text-inverse);
  font-size: var(--mobile-text-base);
  font-weight: 500;
}

/* 提示消息 */
.toast-container {
  position: fixed;
  top: calc(var(--mobile-header-height) + var(--mobile-space-md));
  left: var(--mobile-space-md);
  right: var(--mobile-space-md);
  z-index: 300;
  pointer-events: none;
}

.toast {
  background: var(--mobile-text-primary);
  color: var(--mobile-text-inverse);
  padding: var(--mobile-space-md) var(--mobile-space-lg);
  border-radius: var(--mobile-radius-lg);
  font-size: var(--mobile-text-sm);
  margin-bottom: var(--mobile-space-sm);
  transform: translateY(-100%);
  opacity: 0;
  transition: all var(--mobile-transition-normal);
  pointer-events: auto;
}

.toast.show {
  transform: translateY(0);
  opacity: 1;
}

.toast.success {
  background: var(--mobile-success);
}

.toast.error {
  background: var(--mobile-error);
}

.toast.warning {
  background: var(--mobile-warning);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--mobile-space-md);
  }
  
  .datetime-group {
    flex-direction: column;
    gap: var(--mobile-space-sm);
  }
  
  .passenger-luggage {
    flex-direction: column;
    align-items: stretch;
    gap: var(--mobile-space-sm);
  }
  
  .separator {
    display: none;
  }
}

@media (min-width: 414px) {
  .card-container {
    padding: var(--mobile-space-lg);
  }
  
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --mobile-bg-primary: #1F2937;
    --mobile-bg-secondary: #111827;
    --mobile-bg-card: #1F2937;
    --mobile-text-primary: #F9FAFB;
    --mobile-text-secondary: #D1D5DB;
    --mobile-text-tertiary: #9CA3AF;
    --mobile-border-light: #374151;
    --mobile-border-medium: #4B5563;
    --mobile-border-dark: #6B7280;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  :root {
    --mobile-border-light: #000000;
    --mobile-border-medium: #000000;
    --mobile-text-secondary: #000000;
  }
}