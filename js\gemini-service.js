/**
 * Gemini AI服务模块
 * 负责与Google Gemini API的交互，提供订单内容智能解析功能
 * 支持实时分析和批量处理
 */

import appState from './app-state.js';
import logger from './logger.js';

class GeminiService {
    constructor() {
        this.apiKey = null;
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        this.timeout = 30000;
        
        // 实时分析配置
        this.realtimeConfig = {
            enabled: true,
            debounceDelay: 1500, // 1.5秒防抖延迟
            minInputLength: 20, // 最小输入长度才触发分析
            maxRetries: 2, // 最大重试次数
            confidenceThreshold: 0.3 // 最低置信度阈值
        };
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };
        
        // 订单解析的提示模板
        this.orderParsingPrompt = `
你是一个专业的OTA订单处理助手。请解析以下订单描述，提取关键信息并以JSON格式返回。

请从订单描述中提取以下信息（如果存在）：
1. 客户姓名 (customer_name)
2. 客户联系电话 (customer_contact) - 包括国际区号
3. 客户邮箱 (customer_email)
4. 航班信息 (flight_info) - 航班号
5. 上车地点 (pickup) - 详细地址
6. 目的地 (destination) - 详细地址
7. 日期 (date) - 格式：YYYY-MM-DD
8. 时间 (time) - 格式：HH:MM
9. 乘客人数 (passenger_number) - 数字
10. 行李件数 (luggage_number) - 数字
11. 特殊要求 (extra_requirement) - 包括儿童座椅、导游等需求
12. OTA价格 (ota_price) - 数字
13. 服务类型判断 (service_type) - pickup/dropoff/charter

额外处理规则：
- 自动识别常见地点缩写（KLIA/KLIA2 = 吉隆坡国际机场）
- 电话号码自动添加马来西亚国际区号+60（如果没有区号）
- 根据服务类型推荐sub_category_id：pickup=2, dropoff=3, charter=4
- 根据乘客人数推荐合适车型
- 识别特殊服务需求：baby chair/儿童座椅 -> baby_chair=true

请仅返回JSON格式，不要包含其他文字说明：
`;
    }
    
    /**
     * 设置Gemini API密钥
     * @param {string} apiKey - API密钥
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        appState.set('config.geminiApiKey', apiKey);
        logger.log('Gemini API密钥已设置', 'info');
    }
    
    /**
     * 获取API密钥
     * @returns {string|null} API密钥
     */
    getApiKey() {
        if (this.apiKey) return this.apiKey;
        
        const saved = appState.get('config.geminiApiKey');
        if (saved) {
            this.apiKey = saved;
            return saved;
        }
        
        // 尝试从环境变量获取（如果可用）
        try {
            const envKey = process?.env?.VITE_GEMINI_API_KEY;
            if (envKey) {
                this.setApiKey(envKey);
                return envKey;
            }
        } catch (e) {
            // 忽略环境变量错误
        }
        
        return null;
    }
    
    /**
     * 检查API是否可用
     * @returns {boolean} 是否可用
     */
    isAvailable() {
        return this.getApiKey() !== null;
    }
    
    /**
     * 启用/禁用实时分析
     * @param {boolean} enabled - 是否启用
     */
    setRealtimeAnalysis(enabled) {
        this.realtimeConfig.enabled = enabled;
        appState.set('config.realtimeAnalysis', enabled);
        
        if (!enabled && this.analysisState.currentRequest) {
            this.cancelCurrentAnalysis();
        }
        
        logger.log(`实时分析已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 配置实时分析参数
     * @param {object} config - 配置参数
     */
    configureRealtimeAnalysis(config) {
        this.realtimeConfig = { ...this.realtimeConfig, ...config };
        logger.log('实时分析配置已更新', 'info', this.realtimeConfig);
    }
    
    /**
     * 实时分析订单内容（带防抖）
     * @param {string} orderText - 订单描述文本
     * @param {function} onProgress - 进度回调
     * @param {function} onResult - 结果回调
     * @param {function} onError - 错误回调
     * @returns {Promise<void>}
     */
    async analyzeRealtime(orderText, onProgress, onResult, onError) {
        // 检查是否启用实时分析
        if (!this.realtimeConfig.enabled) {
            return;
        }
        
        // 检查输入长度
        if (!orderText || orderText.trim().length < this.realtimeConfig.minInputLength) {
            this.clearAnalysisState();
            return;
        }
        
        // 检查是否与上次分析的内容相同
        if (orderText.trim() === this.analysisState.lastAnalyzedText) {
            return;
        }
        
        // 取消当前正在进行的分析
        this.cancelCurrentAnalysis();
        
        // 创建新的分析请求
        const analysisId = Date.now();
        this.analysisState.isAnalyzing = true;
        this.analysisState.currentRequest = analysisId;
        
        try {
            // 通知开始分析
            if (onProgress) {
                onProgress('开始智能分析...', 0);
            }
            
            logger.log('开始实时AI分析', 'info', { 
                inputLength: orderText.length,
                analysisId 
            });
            
            // 执行分析
            const result = await this.parseOrderWithRetry(orderText, analysisId, onProgress);
            
            // 检查请求是否已被取消
            if (this.analysisState.currentRequest !== analysisId) {
                logger.log('分析请求已被取消', 'info', { analysisId });
                return;
            }
            
            // 更新分析状态
            this.analysisState.lastAnalyzedText = orderText.trim();
            this.analysisState.isAnalyzing = false;
            this.analysisState.currentRequest = null;
            
            // 记录分析历史
            this.recordAnalysisHistory(orderText, result);
            
            // 通知分析完成
            if (onResult) {
                onResult(result);
            }
            
            logger.log('实时AI分析完成', 'success', { 
                analysisId,
                confidence: result.confidence,
                fieldsExtracted: Object.keys(result.data || {}).length
            });
            
        } catch (error) {
            // 检查请求是否已被取消
            if (this.analysisState.currentRequest !== analysisId) {
                return;
            }
            
            this.analysisState.isAnalyzing = false;
            this.analysisState.currentRequest = null;
            
            logger.log('实时AI分析失败', 'error', { 
                analysisId,
                error: error.message 
            });
            
            // 尝试基本解析作为降级方案
            const fallbackResult = this.basicParse(orderText);
            if (Object.keys(fallbackResult).length > 0) {
                const result = {
                    success: false,
                    data: fallbackResult,
                    confidence: 0.2,
                    error: error.message,
                    fallback: true
                };
                
                if (onResult) {
                    onResult(result);
                }
            } else if (onError) {
                onError(error);
            }
        }
    }
    
    /**
     * 带重试机制的订单解析
     * @param {string} orderText - 订单文本
     * @param {number} analysisId - 分析ID
     * @param {function} onProgress - 进度回调
     * @returns {Promise<object>} 解析结果
     */
    async parseOrderWithRetry(orderText, analysisId, onProgress) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.realtimeConfig.maxRetries + 1; attempt++) {
            try {
                // 检查请求是否已被取消
                if (this.analysisState.currentRequest !== analysisId) {
                    throw new Error('分析请求已被取消');
                }
                
                if (onProgress) {
                    const progress = (attempt - 1) / (this.realtimeConfig.maxRetries + 1) * 80;
                    onProgress(`AI分析中... (尝试 ${attempt})`, progress);
                }
                
                const result = await this.parseOrder(orderText);
                
                // 检查置信度
                if (result.confidence < this.realtimeConfig.confidenceThreshold) {
                    logger.log(`分析置信度过低: ${result.confidence}`, 'warning');
                    
                    if (attempt <= this.realtimeConfig.maxRetries) {
                        continue; // 重试
                    }
                }
                
                if (onProgress) {
                    onProgress('分析完成', 100);
                }
                
                return result;
                
            } catch (error) {
                lastError = error;
                
                if (attempt <= this.realtimeConfig.maxRetries) {
                    logger.log(`分析尝试 ${attempt} 失败，准备重试`, 'warning', { 
                        error: error.message 
                    });
                    
                    // 等待一段时间后重试
                    await this.sleep(1000 * attempt);
                } else {
                    throw lastError;
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 取消当前分析
     */
    cancelCurrentAnalysis() {
        if (this.analysisState.currentRequest) {
            logger.log('取消当前分析请求', 'info', { 
                requestId: this.analysisState.currentRequest 
            });
            
            this.analysisState.currentRequest = null;
            this.analysisState.isAnalyzing = false;
        }
    }
    
    /**
     * 清除分析状态
     */
    clearAnalysisState() {
        this.cancelCurrentAnalysis();
        this.analysisState.lastAnalyzedText = '';
    }
    
    /**
     * 记录分析历史
     * @param {string} input - 输入内容
     * @param {object} result - 分析结果
     */
    recordAnalysisHistory(input, result) {
        const historyItem = {
            timestamp: new Date().toISOString(),
            input: input.substring(0, 100), // 限制长度
            result: {
                success: result.success,
                confidence: result.confidence,
                fieldsCount: Object.keys(result.data || {}).length
            }
        };
        
        this.analysisState.analysisHistory.push(historyItem);
        
        // 保持最近50条记录
        if (this.analysisState.analysisHistory.length > 50) {
            this.analysisState.analysisHistory = this.analysisState.analysisHistory.slice(-50);
        }
    }
    
    /**
     * 获取分析统计信息
     * @returns {object} 统计信息
     */
    getAnalysisStats() {
        const history = this.analysisState.analysisHistory;
        const total = history.length;
        
        if (total === 0) {
            return {
                total: 0,
                successRate: 0,
                averageConfidence: 0,
                averageFields: 0
            };
        }
        
        const successful = history.filter(item => item.result.success).length;
        const totalConfidence = history.reduce((sum, item) => sum + (item.result.confidence || 0), 0);
        const totalFields = history.reduce((sum, item) => sum + (item.result.fieldsCount || 0), 0);
        
        return {
            total,
            successRate: (successful / total * 100).toFixed(1),
            averageConfidence: (totalConfidence / total).toFixed(2),
            averageFields: (totalFields / total).toFixed(1)
        };
    }
    
    /**
     * 发送请求到Gemini API
     * @param {string} prompt - 提示内容
     * @returns {Promise<string>} AI响应
     */
    async request(prompt) {
        const apiKey = this.getApiKey();
        if (!apiKey) {
            throw new Error('Gemini API密钥未设置');
        }
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        try {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1, // 降低温度以提高一致性
                    topK: 20,
                    topP: 0.8,
                    maxOutputTokens: 1024,
                }
            };
            
            logger.log('Gemini API请求', 'info', { promptLength: prompt.length });
            
            const response = await fetch(`${this.baseURL}?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API错误 ${response.status}: ${errorData.error?.message || response.statusText}`);
            }
            
            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('AI未返回有效响应');
            }
            
            const content = data.candidates[0].content?.parts?.[0]?.text;
            if (!content) {
                throw new Error('AI响应格式错误');
            }
            
            logger.log('Gemini API响应成功', 'success', { responseLength: content.length });
            return content;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('AI请求超时');
            }
            
            logger.log('Gemini API错误', 'error', { error: error.message });
            throw error;
        }
    }
    
    /**
     * 解析订单内容
     * @param {string} orderText - 订单描述文本
     * @returns {Promise<object>} 解析结果
     */
    async parseOrder(orderText) {
        if (!orderText || typeof orderText !== 'string') {
            throw new Error('订单内容不能为空');
        }
        
        try {
            logger.log('开始AI解析订单', 'info', { inputLength: orderText.length });
            
            const fullPrompt = `${this.orderParsingPrompt}\n\n订单描述：\n${orderText}`;
            const response = await this.request(fullPrompt);
            
            // 提取JSON内容
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('AI响应中未找到有效的JSON格式');
            }
            
            const parsedData = JSON.parse(jsonMatch[0]);
            
            // 后处理解析结果
            const processedData = this.postProcessParsedData(parsedData);
            
            logger.log('AI解析成功', 'success', { 
                extractedFields: Object.keys(processedData).length,
                data: processedData 
            });
            
            return {
                success: true,
                data: processedData,
                confidence: this.calculateConfidence(processedData),
                rawResponse: response
            };
            
        } catch (error) {
            logger.log('AI解析失败', 'error', { 
                error: error.message, 
                input: orderText.substring(0, 100) + '...' 
            });
            
            throw error;
        }
    }
    
    /**
     * 后处理解析数据
     * @param {object} data - 原始解析数据
     * @returns {object} 处理后的数据
     */
    postProcessParsedData(data) {
        const processed = { ...data };
        
        // 处理电话号码
        if (processed.customer_contact) {
            processed.customer_contact = this.normalizePhoneNumber(processed.customer_contact);
        }
        
        // 处理日期格式
        if (processed.date) {
            processed.date = this.normalizeDate(processed.date);
        }
        
        // 处理时间格式
        if (processed.time) {
            processed.time = this.normalizeTime(processed.time);
        }
        
        // 处理数字字段
        ['passenger_number', 'luggage_number', 'ota_price'].forEach(field => {
            if (processed[field]) {
                const num = parseFloat(processed[field]);
                if (!isNaN(num)) {
                    processed[field] = num;
                }
            }
        });
        
        // 处理布尔字段
        ['baby_chair', 'tour_guide', 'meet_and_greet'].forEach(field => {
            if (processed[field]) {
                processed[field] = Boolean(processed[field]);
            }
        });
        
        // 地点标准化
        processed.pickup = this.normalizeLocation(processed.pickup);
        processed.destination = this.normalizeLocation(processed.destination);
        
        return processed;
    }
    
    /**
     * 标准化电话号码
     * @param {string} phone - 原始电话号码
     * @returns {string} 标准化后的电话号码
     */
    normalizePhoneNumber(phone) {
        if (!phone) return phone;
        
        // 移除所有非数字字符（除了+号）
        let cleaned = phone.replace(/[^\d+]/g, '');
        
        // 如果没有国际区号，添加马来西亚区号
        if (!cleaned.startsWith('+')) {
            if (cleaned.startsWith('60')) {
                cleaned = '+' + cleaned;
            } else if (cleaned.startsWith('0')) {
                cleaned = '+60' + cleaned.substring(1);
            } else {
                cleaned = '+60' + cleaned;
            }
        }
        
        return cleaned;
    }
    
    /**
     * 标准化日期
     * @param {string} date - 原始日期
     * @returns {string} 标准化后的日期 (YYYY-MM-DD)
     */
    normalizeDate(date) {
        if (!date) return date;
        
        try {
            // 尝试解析各种日期格式
            const parsed = new Date(date);
            if (isNaN(parsed)) {
                // 尝试其他格式
                const formats = [
                    /(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})/,  // DD/MM/YYYY or DD-MM-YYYY
                    /(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/   // YYYY/MM/DD or YYYY-MM-DD
                ];
                
                for (const format of formats) {
                    const match = date.match(format);
                    if (match) {
                        const [, a, b, c] = match;
                        // 假设第一种格式是DD/MM/YYYY
                        if (c.length === 4) {
                            return `${c}-${b.padStart(2, '0')}-${a.padStart(2, '0')}`;
                        } else {
                            return `${a}-${b.padStart(2, '0')}-${c.padStart(2, '0')}`;
                        }
                    }
                }
            }
            
            return parsed.toISOString().split('T')[0];
        } catch (error) {
            return date;
        }
    }
    
    /**
     * 标准化时间
     * @param {string} time - 原始时间
     * @returns {string} 标准化后的时间 (HH:MM)
     */
    normalizeTime(time) {
        if (!time) return time;
        
        // 提取时间格式
        const timeMatch = time.match(/(\d{1,2}):?(\d{2})\s*(AM|PM)?/i);
        if (timeMatch) {
            let [, hours, minutes, period] = timeMatch;
            hours = parseInt(hours);
            
            if (period) {
                const isPM = period.toUpperCase() === 'PM';
                if (isPM && hours !== 12) {
                    hours += 12;
                } else if (!isPM && hours === 12) {
                    hours = 0;
                }
            }
            
            return `${hours.toString().padStart(2, '0')}:${minutes}`;
        }
        
        return time;
    }
    
    /**
     * 标准化地点名称
     * @param {string} location - 原始地点
     * @returns {string} 标准化后的地点
     */
    normalizeLocation(location) {
        if (!location) return location;
        
        // 常见地点映射
        const locationMap = {
            'KLIA': '吉隆坡国际机场 (KLIA)',
            'KLIA2': '吉隆坡第二国际机场 (KLIA2)',
            'KUL': '吉隆坡国际机场 (KLIA)',
            'KLCC': '吉隆坡城中城',
            'Bukit Bintang': '武吉免登',
            'Chinatown': '茨厂街唐人街',
            'KL Sentral': '吉隆坡中央车站'
        };
        
        // 检查是否有匹配的标准名称
        for (const [key, value] of Object.entries(locationMap)) {
            if (location.toUpperCase().includes(key.toUpperCase())) {
                return value;
            }
        }
        
        return location;
    }
    
    /**
     * 计算解析置信度
     * @param {object} data - 解析数据
     * @returns {number} 置信度 (0-1)
     */
    calculateConfidence(data) {
        const fields = Object.keys(data);
        const importantFields = ['customer_name', 'pickup', 'destination', 'date', 'time'];
        
        let score = 0;
        let maxScore = 0;
        
        // 基础字段分数
        fields.forEach(field => {
            maxScore += 1;
            if (data[field] && data[field] !== '') {
                score += 1;
                
                // 重要字段额外分数
                if (importantFields.includes(field)) {
                    score += 1;
                    maxScore += 1;
                }
            }
        });
        
        return Math.min(score / maxScore, 1);
    }
    
    /**
     * 基本解析（降级方案）
     * @param {string} text - 订单文本
     * @returns {object} 基本解析结果
     */
    basicParse(text) {
        const result = {};
        
        // 提取电话号码
        const phoneMatch = text.match(/(\+?60)?[\s-]?(\d{2,3})[\s-]?(\d{3,4})[\s-]?(\d{4})/);
        if (phoneMatch) {
            result.customer_contact = phoneMatch[0].replace(/\s/g, '');
        }
        
        // 提取邮箱
        const emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        if (emailMatch) {
            result.customer_email = emailMatch[0];
        }
        
        // 提取日期
        const dateMatch = text.match(/(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})/);
        if (dateMatch) {
            const [, day, month, year] = dateMatch;
            result.date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
        
        // 提取时间
        const timeMatch = text.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
            result.time = `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
        }
        
        // 提取人数
        const passengerMatch = text.match(/(\d+)\s*(?:人|passenger|pax)/i);
        if (passengerMatch) {
            result.passenger_number = parseInt(passengerMatch[1]);
        }
        
        return result;
    }
    
    /**
     * 等待指定时间
     * @param {number} ms - 毫秒数
     * @returns {Promise} Promise对象
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 获取服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            available: this.isAvailable(),
            hasApiKey: this.getApiKey() !== null,
            realtimeEnabled: this.realtimeConfig.enabled,
            isAnalyzing: this.analysisState.isAnalyzing,
            lastUsed: appState.get('system.lastGeminiCall') || null,
            analysisStats: this.getAnalysisStats()
        };
    }
    
    /**
     * 生成示例订单数据
     * @returns {string} 示例订单文本
     */
    generateSampleOrder() {
        const samples = [
            `客户：张三先生 +60123456789
接送：KLIA2机场 到 Pavilion KL购物中心
时间：2024-03-15 14:30
人数：2大1小
要求：需要儿童座椅
价格：RM 120`,
            
            `预订人：李小姐 (<EMAIL>)
航班：MH370 15:45抵达
从：吉隆坡国际机场
到：Bukit Bintang武吉免登
日期：明天下午4点
乘客：3人 + 2件大行李
特殊要求：司机能说中文`,
            
            `Customer: John Smith +60987654321
From: KL Sentral Station  
To: Genting Highlands
Date: 2024-03-20 09:00
Passengers: 4 adults
Luggage: 3 large suitcases
Service: Charter tour (8 hours)
Price: RM 350`
        ];
        
        return samples[Math.floor(Math.random() * samples.length)];
    }
}

// 创建全局Gemini服务实例
const geminiService = new GeminiService();
export default geminiService;