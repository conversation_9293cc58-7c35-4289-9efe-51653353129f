/* 紧凑型订单预览与编辑界面样式 */

/* CSS变量定义 */
:root {
  /* 紧凑型颜色系统 */
  --compact-primary: #3B82F6;
  --compact-primary-light: #DBEAFE;
  --compact-secondary: #6B7280;
  --compact-success: #10B981;
  --compact-warning: #F59E0B;
  --compact-error: #EF4444;
  
  /* 背景色 */
  --compact-bg-primary: #FFFFFF;
  --compact-bg-secondary: #F8FAFC;
  --compact-bg-tertiary: #F1F5F9;
  --compact-bg-accent: #EFF6FF;
  
  /* 文字色 */
  --compact-text-primary: #1F2937;
  --compact-text-secondary: #6B7280;
  --compact-text-tertiary: #9CA3AF;
  --compact-text-accent: #3B82F6;
  
  /* 边框色 */
  --compact-border-light: #E5E7EB;
  --compact-border-medium: #D1D5DB;
  --compact-border-accent: #3B82F6;
  
  /* 阴影 */
  --compact-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --compact-shadow-md: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  --compact-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* 紧凑型间距 */
  --compact-space-xs: 2px;
  --compact-space-sm: 4px;
  --compact-space-md: 6px;
  --compact-space-lg: 8px;
  --compact-space-xl: 12px;
  --compact-space-2xl: 16px;
  
  /* 紧凑型字体 */
  --compact-text-xs: 10px;
  --compact-text-sm: 11px;
  --compact-text-base: 12px;
  --compact-text-lg: 13px;
  --compact-text-xl: 14px;
  
  /* 圆角 */
  --compact-radius-sm: 3px;
  --compact-radius-md: 4px;
  --compact-radius-lg: 6px;
  
  /* 过渡 */
  --compact-transition: 150ms ease-out;
  
  /* 布局尺寸 */
  --compact-header-height: 48px;
  --compact-footer-height: 56px;
  --compact-column-width: 320px;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--compact-text-base);
  line-height: 1.3;
  color: var(--compact-text-primary);
  background-color: var(--compact-bg-secondary);
}

/* 主容器 */
.compact-order-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 顶部工具栏 */
.compact-header {
  height: var(--compact-header-height);
  background: var(--compact-bg-primary);
  border-bottom: 1px solid var(--compact-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--compact-space-2xl);
  flex-shrink: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--compact-space-xl);
}

.back-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--compact-bg-tertiary);
  border-radius: var(--compact-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--compact-transition);
  font-size: var(--compact-text-lg);
  color: var(--compact-text-secondary);
}

.back-btn:hover {
  background: var(--compact-border-medium);
  transform: translateX(-1px);
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-xs);
}

.order-id {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
  font-family: 'Courier New', monospace;
}

.order-status {
  font-size: var(--compact-text-xs);
  color: var(--compact-text-secondary);
  background: var(--compact-bg-accent);
  padding: var(--compact-space-xs) var(--compact-space-md);
  border-radius: var(--compact-radius-sm);
  width: fit-content;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--compact-space-lg);
}

.edit-btn, .save-btn, .reset-btn {
  height: 32px;
  padding: 0 var(--compact-space-xl);
  border: 1px solid var(--compact-border-medium);
  border-radius: var(--compact-radius-md);
  font-size: var(--compact-text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--compact-transition);
  background: var(--compact-bg-primary);
  color: var(--compact-text-primary);
}

.edit-btn:hover, .reset-btn:hover {
  border-color: var(--compact-border-accent);
  color: var(--compact-text-accent);
}

.save-btn {
  background: var(--compact-primary);
  border-color: var(--compact-primary);
  color: white;
}

.save-btn:hover {
  background: #2563EB;
  border-color: #2563EB;
}

/* 主要内容区 - 三列布局 */
.compact-main {
  flex: 1;
  display: grid;
  grid-template-columns: var(--compact-column-width) var(--compact-column-width) var(--compact-column-width);
  gap: 1px;
  background: var(--compact-border-light);
  overflow: hidden;
}

/* 信息列 */
.info-column {
  background: var(--compact-bg-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.column-header {
  height: 40px;
  background: var(--compact-bg-tertiary);
  border-bottom: 1px solid var(--compact-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--compact-space-xl);
  flex-shrink: 0;
}

.column-header h3 {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
}

.smart-indicators {
  display: flex;
  gap: var(--compact-space-sm);
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--compact-success);
  font-size: 0;
}

.indicator.warning {
  background: var(--compact-warning);
}

.indicator.error {
  background: var(--compact-error);
}

/* 信息网格 */
.info-grid {
  padding: var(--compact-space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-xl);
  overflow-y: auto;
  flex: 1;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-sm);
}

.field-group.required label::after {
  content: ' *';
  color: var(--compact-error);
}

.field-group label {
  font-size: var(--compact-text-xs);
  font-weight: 500;
  color: var(--compact-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-content {
  position: relative;
}

.display-value {
  display: block;
  font-size: var(--compact-text-sm);
  color: var(--compact-text-primary);
  font-weight: 500;
  padding: var(--compact-space-md) var(--compact-space-lg);
  background: var(--compact-bg-secondary);
  border-radius: var(--compact-radius-md);
  border: 1px solid transparent;
  transition: all var(--compact-transition);
  cursor: pointer;
}

.display-value:hover {
  border-color: var(--compact-border-medium);
  background: var(--compact-bg-tertiary);
}

.edit-input, .edit-select, .edit-textarea {
  display: none;
  width: 100%;
  padding: var(--compact-space-md) var(--compact-space-lg);
  border: 1px solid var(--compact-border-medium);
  border-radius: var(--compact-radius-md);
  font-size: var(--compact-text-sm);
  background: var(--compact-bg-primary);
  color: var(--compact-text-primary);
  transition: border-color var(--compact-transition);
}

.edit-input:focus, .edit-select:focus, .edit-textarea:focus {
  outline: none;
  border-color: var(--compact-border-accent);
  box-shadow: 0 0 0 2px var(--compact-primary-light);
}

.smart-select {
  position: relative;
}

.smart-hint {
  font-size: var(--compact-text-xs);
  color: var(--compact-text-accent);
  margin-top: var(--compact-space-xs);
  display: flex;
  align-items: center;
  gap: var(--compact-space-xs);
}

.smart-hint::before {
  content: '🤖';
  font-size: var(--compact-text-xs);
}

/* 编辑模式 */
.compact-order-app.edit-mode .display-value {
  display: none;
}

.compact-order-app.edit-mode .edit-input,
.compact-order-app.edit-mode .edit-select,
.compact-order-app.edit-mode .edit-textarea {
  display: block;
}

/* 路线可视化 */
.route-visual {
  padding: var(--compact-space-xl);
  display: flex;
  align-items: center;
  gap: var(--compact-space-lg);
  background: var(--compact-bg-accent);
  margin: var(--compact-space-xl);
  border-radius: var(--compact-radius-lg);
}

.route-point {
  display: flex;
  align-items: center;
  gap: var(--compact-space-lg);
  flex: 1;
}

.point-marker {
  width: 24px;
  height: 24px;
  background: var(--compact-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--compact-text-sm);
  font-weight: 600;
  flex-shrink: 0;
}

.point-details {
  flex: 1;
}

.route-arrow {
  font-size: var(--compact-text-lg);
  color: var(--compact-text-secondary);
  font-weight: bold;
}

/* 行程详情 */
.trip-details {
  padding: var(--compact-space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-xl);
  flex: 1;
  overflow-y: auto;
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--compact-space-lg);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-sm);
}

/* 客户信息 */
.customer-info {
  padding: var(--compact-space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-xl);
  flex: 1;
  overflow-y: auto;
}

/* 费用信息 */
.pricing-section {
  padding: var(--compact-space-xl);
  border-top: 1px solid var(--compact-border-light);
}

.pricing-section h4 {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
  margin-bottom: var(--compact-space-xl);
}

.pricing-grid {
  display: flex;
  flex-direction: column;
  gap: var(--compact-space-lg);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-item label {
  font-size: var(--compact-text-sm);
  color: var(--compact-text-secondary);
  margin: 0;
  text-transform: none;
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--compact-space-lg);
  border-top: 1px solid var(--compact-border-light);
  margin-top: var(--compact-space-lg);
}

.price-total label {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
}

.total-amount {
  font-size: var(--compact-text-lg);
  font-weight: 700;
  color: var(--compact-primary);
}

/* 特殊要求 */
.requirements-section {
  padding: var(--compact-space-xl);
  border-top: 1px solid var(--compact-border-light);
}

.requirements-section h4 {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
  margin-bottom: var(--compact-space-xl);
}

.requirements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--compact-space-lg);
  margin-bottom: var(--compact-space-xl);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: var(--compact-space-md);
  cursor: pointer;
  font-size: var(--compact-text-sm);
}

.checkbox-item input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: var(--compact-primary);
}

/* 底部操作栏 */
.compact-footer {
  height: var(--compact-footer-height);
  background: var(--compact-bg-primary);
  border-top: 1px solid var(--compact-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--compact-space-2xl);
  flex-shrink: 0;
}

.footer-left, .footer-center, .footer-right {
  display: flex;
  align-items: center;
  gap: var(--compact-space-lg);
}

.validation-status {
  display: flex;
  align-items: center;
  gap: var(--compact-space-md);
  font-size: var(--compact-text-sm);
}

.status-icon {
  color: var(--compact-success);
  font-weight: bold;
}

.status-text {
  color: var(--compact-text-secondary);
}

.action-btn {
  height: 36px;
  padding: 0 var(--compact-space-2xl);
  border: 1px solid var(--compact-border-medium);
  border-radius: var(--compact-radius-md);
  font-size: var(--compact-text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--compact-transition);
  background: var(--compact-bg-primary);
  color: var(--compact-text-primary);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--compact-shadow-md);
}

.preview-btn:hover, .validate-btn:hover {
  border-color: var(--compact-border-accent);
  color: var(--compact-text-accent);
}

.cancel-btn:hover {
  border-color: var(--compact-error);
  color: var(--compact-error);
}

.confirm-btn {
  background: var(--compact-primary);
  border-color: var(--compact-primary);
  color: white;
}

.confirm-btn:hover {
  background: #2563EB;
  border-color: #2563EB;
}

/* 智能提示浮层 */
.smart-tooltip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--compact-bg-primary);
  border: 1px solid var(--compact-border-medium);
  border-radius: var(--compact-radius-lg);
  box-shadow: var(--compact-shadow-lg);
  z-index: 200;
  min-width: 280px;
  max-width: 400px;
}

.tooltip-content {
  padding: var(--compact-space-2xl);
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: var(--compact-space-lg);
  margin-bottom: var(--compact-space-xl);
}

.tooltip-icon {
  font-size: var(--compact-text-lg);
}

.tooltip-title {
  font-size: var(--compact-text-lg);
  font-weight: 600;
  color: var(--compact-text-primary);
}

.tooltip-body {
  font-size: var(--compact-text-sm);
  color: var(--compact-text-secondary);
  line-height: 1.4;
}

/* 快速搜索 */
.quick-search {
  position: absolute;
  top: var(--compact-header-height);
  right: var(--compact-space-2xl);
  width: 280px;
  background: var(--compact-bg-primary);
  border: 1px solid var(--compact-border-medium);
  border-radius: var(--compact-radius-lg);
  box-shadow: var(--compact-shadow-lg);
  z-index: 150;
}

.quick-search input {
  width: 100%;
  padding: var(--compact-space-xl);
  border: none;
  border-bottom: 1px solid var(--compact-border-light);
  font-size: var(--compact-text-sm);
  background: transparent;
  color: var(--compact-text-primary);
}

.quick-search input:focus {
  outline: none;
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: var(--compact-space-lg) var(--compact-space-xl);
  cursor: pointer;
  font-size: var(--compact-text-sm);
  color: var(--compact-text-primary);
  border-bottom: 1px solid var(--compact-border-light);
  transition: background-color var(--compact-transition);
}

.search-result-item:hover {
  background: var(--compact-bg-secondary);
}

.search-result-item:last-child {
  border-bottom: none;
}

/* 加载指示器 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 300;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--compact-space-xl);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--compact-border-light);
  border-top: 3px solid var(--compact-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--compact-text-sm);
  color: var(--compact-text-secondary);
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .compact-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    overflow-y: auto;
  }
  
  .info-column {
    min-height: auto;
  }
  
  .route-visual {
    flex-direction: column;
    gap: var(--compact-space-md);
  }
  
  .route-arrow {
    transform: rotate(90deg);
  }
  
  .detail-row {
    grid-template-columns: 1fr;
  }
  
  .requirements-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .compact-header {
    padding: 0 var(--compact-space-xl);
  }
  
  .header-left, .header-right {
    gap: var(--compact-space-md);
  }
  
  .compact-footer {
    flex-direction: column;
    height: auto;
    padding: var(--compact-space-xl);
    gap: var(--compact-space-lg);
  }
  
  .footer-left, .footer-center, .footer-right {
    width: 100%;
    justify-content: center;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --compact-bg-primary: #1F2937;
    --compact-bg-secondary: #111827;
    --compact-bg-tertiary: #374151;
    --compact-bg-accent: #1E3A8A;
    --compact-text-primary: #F9FAFB;
    --compact-text-secondary: #D1D5DB;
    --compact-text-tertiary: #9CA3AF;
    --compact-border-light: #374151;
    --compact-border-medium: #4B5563;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  :root {
    --compact-border-light: #000000;
    --compact-border-medium: #000000;
    --compact-text-secondary: #000000;
  }
}