<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>订单预览 - 移动端优化</title>
    <link rel="stylesheet" href="mobile-order-styles.css">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
</head>
<body>
    <div id="mobileOrderApp" class="mobile-order-app">
        <!-- 顶部导航栏 - 紧凑设计 -->
        <header class="mobile-header">
            <div class="header-content">
                <button class="back-btn" id="backBtn">
                    <span class="icon">←</span>
                </button>
                <h1 class="page-title">订单预览</h1>
                <div class="header-actions">
                    <button class="edit-toggle-btn" id="editToggleBtn">
                        <span class="icon">✏️</span>
                    </button>
                    <button class="save-btn" id="saveBtn" style="display: none;">
                        <span class="icon">✓</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区 - 滚动容器 -->
        <main class="mobile-main">
            <!-- 订单状态指示器 -->
            <div class="status-indicator">
                <div class="status-badge" id="orderStatus">
                    <span class="status-icon">📋</span>
                    <span class="status-text">待确认</span>
                </div>
                <div class="order-id" id="orderId">GMH-240315001</div>
            </div>

            <!-- 核心信息卡片组 -->
            <div class="card-container">
                <!-- 基本信息卡片 -->
                <div class="info-card basic-info">
                    <div class="card-header">
                        <span class="card-icon">📝</span>
                        <h3 class="card-title">基本信息</h3>
                        <button class="expand-btn" data-target="basic-info">
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content" id="basic-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <label>订单号</label>
                                <div class="editable-field" data-field="ota_reference_number">
                                    <span class="display-value">GMH-240315001</span>
                                    <input type="text" class="edit-input" value="GMH-240315001">
                                </div>
                            </div>
                            <div class="info-item">
                                <label>创建时间</label>
                                <span class="value">2024-03-15 14:30</span>
                            </div>
                            <div class="info-item">
                                <label>服务类型</label>
                                <div class="editable-field" data-field="sub_category_id">
                                    <span class="display-value">接机服务</span>
                                    <select class="edit-select">
                                        <option value="2" selected>接机服务</option>
                                        <option value="3">送机服务</option>
                                        <option value="4">包车服务</option>
                                    </select>
                                </div>
                            </div>
                            <div class="info-item">
                                <label>负责人</label>
                                <div class="editable-field" data-field="incharge_by_backend_user_id">
                                    <span class="display-value">Jcy</span>
                                    <select class="edit-select">
                                        <option value="310" selected>Jcy</option>
                                        <option value="311">opAnnie</option>
                                        <option value="37">smw</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 行程信息卡片 -->
                <div class="info-card trip-info">
                    <div class="card-header">
                        <span class="card-icon">🚗</span>
                        <h3 class="card-title">行程信息</h3>
                        <button class="expand-btn" data-target="trip-info">
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content" id="trip-info">
                        <!-- 路线可视化 -->
                        <div class="route-visual">
                            <div class="route-point start">
                                <div class="point-icon">🛫</div>
                                <div class="point-info">
                                    <div class="editable-field" data-field="pickup">
                                        <span class="display-value">KLIA2国际机场</span>
                                        <input type="text" class="edit-input" value="KLIA2国际机场">
                                    </div>
                                    <small class="point-label">上车地点</small>
                                </div>
                            </div>
                            <div class="route-line">
                                <div class="route-dots">• • •</div>
                            </div>
                            <div class="route-point end">
                                <div class="point-icon">🏨</div>
                                <div class="point-info">
                                    <div class="editable-field" data-field="destination">
                                        <span class="display-value">吉隆坡双子塔</span>
                                        <input type="text" class="edit-input" value="吉隆坡双子塔">
                                    </div>
                                    <small class="point-label">目的地</small>
                                </div>
                            </div>
                        </div>

                        <!-- 时间和车型信息 -->
                        <div class="trip-details">
                            <div class="detail-row">
                                <span class="detail-icon">📅</span>
                                <div class="detail-content">
                                    <label>日期时间</label>
                                    <div class="datetime-group">
                                        <div class="editable-field" data-field="date">
                                            <span class="display-value">2024-03-15</span>
                                            <input type="date" class="edit-input" value="2024-03-15">
                                        </div>
                                        <div class="editable-field" data-field="time">
                                            <span class="display-value">14:30</span>
                                            <input type="time" class="edit-input" value="14:30">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <span class="detail-icon">🚙</span>
                                <div class="detail-content">
                                    <label>车型</label>
                                    <div class="editable-field" data-field="car_type_id">
                                        <span class="display-value">5座舒适型</span>
                                        <select class="edit-select">
                                            <option value="5" selected>5座舒适型</option>
                                            <option value="15">7座MPV</option>
                                            <option value="20">10座商务车</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <span class="detail-icon">👥</span>
                                <div class="detail-content">
                                    <label>乘客/行李</label>
                                    <div class="passenger-luggage">
                                        <div class="editable-field" data-field="passenger_number">
                                            <span class="display-value">3人</span>
                                            <input type="number" class="edit-input" value="3" min="1" max="50">
                                        </div>
                                        <span class="separator">/</span>
                                        <div class="editable-field" data-field="luggage_number">
                                            <span class="display-value">2件</span>
                                            <input type="number" class="edit-input" value="2" min="0" max="50">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 客户信息卡片 -->
                <div class="info-card customer-info">
                    <div class="card-header">
                        <span class="card-icon">👤</span>
                        <h3 class="card-title">客户信息</h3>
                        <button class="expand-btn" data-target="customer-info">
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content collapsed" id="customer-info">
                        <div class="customer-details">
                            <div class="customer-row">
                                <span class="customer-icon">👤</span>
                                <div class="customer-content">
                                    <label>姓名</label>
                                    <div class="editable-field" data-field="customer_name">
                                        <span class="display-value">张三</span>
                                        <input type="text" class="edit-input" value="张三">
                                    </div>
                                </div>
                            </div>
                            <div class="customer-row">
                                <span class="customer-icon">📞</span>
                                <div class="customer-content">
                                    <label>电话</label>
                                    <div class="editable-field" data-field="customer_contact">
                                        <span class="display-value">+60123456789</span>
                                        <input type="tel" class="edit-input" value="+60123456789">
                                    </div>
                                </div>
                            </div>
                            <div class="customer-row">
                                <span class="customer-icon">✈️</span>
                                <div class="customer-content">
                                    <label>航班</label>
                                    <div class="editable-field" data-field="flight_info">
                                        <span class="display-value">MH123</span>
                                        <input type="text" class="edit-input" value="MH123">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费用信息卡片 -->
                <div class="info-card pricing-info">
                    <div class="card-header">
                        <span class="card-icon">💰</span>
                        <h3 class="card-title">费用明细</h3>
                        <button class="expand-btn" data-target="pricing-info">
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content collapsed" id="pricing-info">
                        <div class="pricing-breakdown">
                            <div class="price-row">
                                <span class="price-label">OTA价格</span>
                                <div class="editable-field" data-field="ota_price">
                                    <span class="display-value">RM 120.00</span>
                                    <input type="number" class="edit-input" value="120" step="0.01">
                                </div>
                            </div>
                            <div class="price-row">
                                <span class="price-label">司机费用</span>
                                <div class="editable-field" data-field="driver_fee">
                                    <span class="display-value">RM 100.00</span>
                                    <input type="number" class="edit-input" value="100" step="0.01">
                                </div>
                            </div>
                            <div class="price-row">
                                <span class="price-label">司机代收</span>
                                <div class="editable-field" data-field="driver_collect">
                                    <span class="display-value">RM 20.00</span>
                                    <input type="number" class="edit-input" value="20" step="0.01">
                                </div>
                            </div>
                            <div class="price-row total">
                                <span class="price-label">总计</span>
                                <span class="price-value total-amount">RM 120.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 特殊要求卡片 -->
                <div class="info-card special-requirements">
                    <div class="card-header">
                        <span class="card-icon">⭐</span>
                        <h3 class="card-title">特殊要求</h3>
                        <button class="expand-btn" data-target="special-requirements">
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content collapsed" id="special-requirements">
                        <div class="requirements-list">
                            <div class="requirement-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" class="requirement-checkbox" data-field="baby_chair" checked>
                                    <span class="checkbox-custom"></span>
                                    <span class="requirement-text">儿童座椅</span>
                                </label>
                            </div>
                            <div class="requirement-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" class="requirement-checkbox" data-field="tour_guide">
                                    <span class="checkbox-custom"></span>
                                    <span class="requirement-text">导游服务</span>
                                </label>
                            </div>
                            <div class="requirement-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" class="requirement-checkbox" data-field="meet_and_greet">
                                    <span class="checkbox-custom"></span>
                                    <span class="requirement-text">接机服务</span>
                                </label>
                            </div>
                            <div class="requirement-note">
                                <label>备注</label>
                                <div class="editable-field" data-field="extra_requirement">
                                    <span class="display-value">需要中文司机</span>
                                    <textarea class="edit-textarea" rows="2">需要中文司机</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部操作栏 -->
        <footer class="mobile-footer">
            <div class="footer-actions">
                <button class="action-btn secondary" id="cancelBtn">
                    <span class="btn-icon">✕</span>
                    <span class="btn-text">取消</span>
                </button>
                <button class="action-btn primary" id="confirmBtn">
                    <span class="btn-icon">✓</span>
                    <span class="btn-text">确认订单</span>
                </button>
            </div>
        </footer>

        <!-- 快速操作浮动按钮 -->
        <div class="fab-container">
            <button class="fab main-fab" id="mainFab">
                <span class="fab-icon">⚡</span>
            </button>
            <div class="fab-menu" id="fabMenu">
                <button class="fab sub-fab" data-action="duplicate">
                    <span class="fab-icon">📋</span>
                    <span class="fab-label">复制</span>
                </button>
                <button class="fab sub-fab" data-action="share">
                    <span class="fab-icon">📤</span>
                    <span class="fab-label">分享</span>
                </button>
                <button class="fab sub-fab" data-action="history">
                    <span class="fab-icon">📜</span>
                    <span class="fab-label">历史</span>
                </button>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p class="loading-text">处理中...</p>
            </div>
        </div>

        <!-- 提示消息容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="mobile-order-preview.js"></script>
</body>
</html>